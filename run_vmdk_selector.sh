#!/bin/bash

echo "VMDK文件选择器"
echo "================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.6或更高版本"
    echo "Ubuntu/Debian: sudo apt install python3"
    echo "CentOS/RHEL: sudo yum install python3"
    echo "macOS: brew install python3"
    exit 1
fi

# 检查脚本文件是否存在
if [ ! -f "vmdk_selector.py" ]; then
    echo "错误: 未找到vmdk_selector.py文件"
    echo "请确保该文件与此脚本在同一目录中"
    exit 1
fi

# 运行Python脚本
echo "正在分析VMDK文件..."
echo
python3 vmdk_selector.py

echo
echo "分析完成！"
echo

# 如果是交互式终端，等待用户按键
if [ -t 0 ]; then
    read -p "按Enter键退出..."
fi
