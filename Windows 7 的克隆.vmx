.encoding = "GBK"
config.version = "8"
virtualHW.version = "19"

# 处理器配置
numvcpus = "2"
cpuid.coresPerSocket = "2"

# 内存配置
memsize = "2048"
MemAllowAutoScaleDown = "FALSE"
MemTrimRate = "-1"

# 虚拟机基本信息
displayName = "Windows 7 的克隆"
guestOS = "windows7"
uuid.location = "56 4d 37 c2 9c 37 50 03-35 e0 85 2f 30 27 ea c2"
uuid.bios = "56 4d 37 c2 9c 37 50 03-35 e0 85 2f 30 27 ea c2"

# 电源管理
powerType.powerOff = "soft"
powerType.powerOn = "soft"
powerType.suspend = "soft"
powerType.reset = "soft"
cleanShutdown = "TRUE"

# SCSI控制器和硬盘
scsi0.present = "TRUE"
scsi0.virtualDev = "lsilogic"
scsi0:0.present = "TRUE"
scsi0:0.deviceType = "scsi-hardDisk"
scsi0:0.fileName = "/mnt/vm/vm_lonele/Windows 7-cl1-000002.vmdk"

# IDE控制器和光驱
ide1:0.present = "TRUE"
ide1:0.deviceType = "cdrom-image"
ide1:0.startConnected = "FALSE"
ide1:0.autodetect = "TRUE"

# 软驱
floppy0.present = "FALSE"

# 网络适配器
ethernet0.present = "TRUE"
ethernet0.virtualDev = "e1000"
ethernet0.networkName = "NAT"
ethernet0.addressType = "generated"
ethernet0.generatedAddress = "00:0c:29:27:ea:c2"
ethernet0.generatedAddressOffset = "0"

# 声卡
sound.present = "TRUE"
sound.virtualDev = "hdaudio"
sound.fileName = "-1"
sound.autodetect = "TRUE"

# USB控制器
usb.present = "TRUE"
ehci.present = "TRUE"
usb_xhci.present = "TRUE"

# 显卡
svga.present = "TRUE"
svga.graphicsMemoryKB = "786432"

# PCI桥接器
pciBridge0.present = "TRUE"
pciBridge4.present = "TRUE"
pciBridge4.virtualDev = "pcieRootPort"
pciBridge4.functions = "8"
pciBridge5.present = "TRUE"
pciBridge5.virtualDev = "pcieRootPort"
pciBridge5.functions = "8"
pciBridge6.present = "TRUE"
pciBridge6.virtualDev = "pcieRootPort"
pciBridge6.functions = "8"
pciBridge7.present = "TRUE"
pciBridge7.virtualDev = "pcieRootPort"
pciBridge7.functions = "8"

# 其他设备
vmci0.present = "TRUE"
hpet0.present = "TRUE"

# 文件路径
nvram = "/mnt/vm/vm_lonele/Windows 7 的克隆.nvram"

# 快照状态
checkpoint.vmState = "/mnt/vm/vm_lonele/Windows 7 的克隆-Snapshot2.vmsn"

# 兼容性
virtualHW.productCompatibility = "hosted"
replay.supported = "FALSE"
replay.filename = ""
