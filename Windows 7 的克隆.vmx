#!/usr/bin/vmware
.encoding = "GBK"
config.version = "8"
virtualHW.version = "19"
pciBridge0.present = "TRUE"
pciBridge4.present = "TRUE"
pciBridge4.virtualDev = "pcieRootPort"
pciBridge4.functions = "8"
pciBridge5.present = "TRUE"
pciBridge5.virtualDev = "pcieRootPort"
pciBridge5.functions = "8"
pciBridge6.present = "TRUE"
pciBridge6.virtualDev = "pcieRootPort"
pciBridge6.functions = "8"
pciBridge7.present = "TRUE"
pciBridge7.virtualDev = "pcieRootPort"
pciBridge7.functions = "8"
vmci0.present = "TRUE"
hpet0.present = "TRUE"
nvram = "Windows 7 的克隆.nvram"
virtualHW.productCompatibility = "hosted"
powerType.powerOff = "soft"
powerType.powerOn = "soft"
powerType.suspend = "soft"
powerType.reset = "soft"
displayName = "Windows 7 的克隆"
guestOS = "windows7"
numvcpus = "2"
cpuid.coresPerSocket = "2"
memsize = "2048"
MemAllowAutoScaleDown = "FALSE"
MemTrimRate = "-1"
uuid.location = "56 4d 37 c2 9c 37 50 03-35 e0 85 2f 30 27 ea c2"
uuid.bios = "56 4d 37 c2 9c 37 50 03-35 e0 85 2f 30 27 ea c2"
cleanShutdown = "TRUE"
replay.supported = "FALSE"
replay.filename = ""
scsi0.virtualDev = "lsilogic"
scsi0.present = "TRUE"
scsi0:0.deviceType = "scsi-hardDisk"
scsi0:0.fileName = "Windows 7-cl1-000002.vmdk"
scsi0:0.present = "TRUE"
ide1:0.present = "TRUE"
ide1:0.deviceType = "cdrom-raw"
ide1:0.startConnected = "FALSE"
ide1:0.autodetect = "TRUE"
floppy0.startConnected = "FALSE"
floppy0.autodetect = "TRUE"
ethernet0.present = "TRUE"
ethernet0.virtualDev = "e1000"
ethernet0.networkName = "NAT"
ethernet0.addressType = "generated"
ethernet0.generatedAddress = "00:0c:29:27:ea:c2"
ethernet0.generatedAddressOffset = "0"
sound.present = "TRUE"
sound.virtualDev = "hdaudio"
sound.fileName = "-1"
sound.autodetect = "TRUE"
usb.present = "TRUE"
ehci.present = "TRUE"
usb_xhci.present = "TRUE"
svga.graphicsMemoryKB = "786432"
checkpoint.vmState = "Windows 7 的克隆-Snapshot2.vmsn"
