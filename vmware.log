2025-06-12T13:54:52.503Z In(05) vmx Log for VMware Workstation pid=42376 version=17.5.2 build=build-23775571 option=Release
2025-06-12T13:54:52.503Z In(05) vmx The host is x86_64.
2025-06-12T13:54:52.503Z In(05) vmx Host codepage=UTF-8 encoding=UTF-8
2025-06-12T13:54:52.503Z In(05) vmx Host is Linux 6.8.0-60-generic Ubuntu 24.04.1 LTS Ubuntu 24.04 24.04.1 LTS (Noble Numbat)
2025-06-12T13:54:52.503Z In(05) vmx Host offset from UTC is +08:00.
2025-06-12T13:54:52.465Z In(05) vmx VTHREAD 123947955620864 "vmx" tid 42376
2025-06-12T13:54:52.466Z In(05) vmx LOCALE zh_CN.UTF-8 -> zh_CN
2025-06-12T13:54:52.466Z In(05) vmx Msg_SetLocaleEx: HostLocale=UTF-8 UserLocale=zh_CN
2025-06-12T13:54:52.466Z In(05) vmx PREF early PreferenceGet(libdir), using default
2025-06-12T13:54:52.466Z In(05) vmx DictionaryLoad: Cannot open file "/usr/lib/vmware/messages/zh_CN/vmware.vmsg": No such file or directory.
2025-06-12T13:54:52.466Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/usr/lib/vmware/messages/zh_CN/vmware.vmsg": No such file or directory.
2025-06-12T13:54:52.466Z Wa(03) vmx Cannot load message dictionary "/usr/lib/vmware/messages/zh_CN/vmware.vmsg".
2025-06-12T13:54:52.466Z In(05) vmx DictionaryLoad: Cannot open file "/usr/lib/vmware/settings": No such file or directory.
2025-06-12T13:54:52.466Z In(05) vmx Msg_Reset:
2025-06-12T13:54:52.466Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/usr/lib/vmware/settings": No such file or directory.
2025-06-12T13:54:52.466Z In(05) vmx ----------------------------------------
2025-06-12T13:54:52.466Z In(05) vmx ConfigDB: Failed to load /usr/lib/vmware/settings
2025-06-12T13:54:52.466Z In(05) vmx DictionaryLoad: Cannot open file "/home/<USER>/.vmware/config": No such file or directory.
2025-06-12T13:54:52.466Z In(05) vmx Msg_Reset:
2025-06-12T13:54:52.466Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/home/<USER>/.vmware/config": No such file or directory.
2025-06-12T13:54:52.466Z In(05) vmx ----------------------------------------
2025-06-12T13:54:52.466Z In(05) vmx ConfigDB: Failed to load ~/.vmware/config
2025-06-12T13:54:52.466Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2025-06-12T13:54:52.466Z In(05) vmx DictionaryLoad: Cannot open file "/usr/lib/vmware/settings": No such file or directory.
2025-06-12T13:54:52.466Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/usr/lib/vmware/settings": No such file or directory.
2025-06-12T13:54:52.466Z In(05) vmx PREF Optional preferences file not found at /usr/lib/vmware/settings. Using default values.
2025-06-12T13:54:52.466Z In(05) vmx DictionaryLoad: Cannot open file "/home/<USER>/.vmware/config": No such file or directory.
2025-06-12T13:54:52.466Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/home/<USER>/.vmware/config": No such file or directory.
2025-06-12T13:54:52.466Z In(05) vmx PREF Optional preferences file not found at /home/<USER>/.vmware/config. Using default values.
2025-06-12T13:54:52.467Z In(05) vmx lib/ssl: OpenSSL using default provider
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: Client usage
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: protocol list tls1.2
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: Server usage
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: protocol list tls1.2
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-06-12T13:54:52.468Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-06-12T13:54:52.557Z In(05) vmx Hostname=geyee-jiguangxseriesgm6aq7c.lan
2025-06-12T13:54:52.557Z In(05) vmx IP=127.0.0.1 (lo)
2025-06-12T13:54:52.557Z In(05) vmx IP=************ (wlo1)
2025-06-12T13:54:52.557Z In(05) vmx IP=************* (virbr0)
2025-06-12T13:54:52.557Z In(05) vmx IP=************* (tailscale0)
2025-06-12T13:54:52.557Z In(05) vmx IP=************ (ztr4nyvtab)
2025-06-12T13:54:52.557Z In(05) vmx IP=************* (vmnet1)
2025-06-12T13:54:52.557Z In(05) vmx IP=*********** (vmnet8)
2025-06-12T13:54:52.557Z In(05) vmx IP=********** (br-5f7e0aa4f2ef)
2025-06-12T13:54:52.557Z In(05) vmx IP=********** (docker0)
2025-06-12T13:54:52.557Z In(05) vmx IP=********** (br-7988e585751d)
2025-06-12T13:54:52.557Z In(05) vmx IP=********** (br-ac68962a481d)
2025-06-12T13:54:52.557Z In(05) vmx IP=********** (br-5dcf2693630a)
2025-06-12T13:54:52.557Z In(05) vmx IP=************* (tun1)
2025-06-12T13:54:52.558Z In(05) vmx System uptime ********** us
2025-06-12T13:54:52.558Z In(05) vmx Command line: "/usr/lib/vmware/bin/vmware-vmx" "-s" "vmx.stdio.keep=TRUE" "-#" "product=1;name=VMware Workstation;version=17.5.2;buildnumber=23775571;licensename=VMware Workstation;licenseversion=17.0;" "-@" "duplex=3;msgs=ui" "/mnt/vm/vm_lonele/Windows7_Clone_Simple.vmx"
2025-06-12T13:54:52.558Z In(05) vmx Msg_SetLocaleEx: HostLocale=UTF-8 UserLocale=zh_CN
2025-06-12T13:54:52.558Z In(05) vmx DictionaryLoad: Cannot open file "/usr/lib/vmware/messages/zh_CN/vmware.vmsg": No such file or directory.
2025-06-12T13:54:52.558Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/usr/lib/vmware/messages/zh_CN/vmware.vmsg": No such file or directory.
2025-06-12T13:54:52.558Z Wa(03) vmx Cannot load message dictionary "/usr/lib/vmware/messages/zh_CN/vmware.vmsg".
2025-06-12T13:54:52.558Z In(05) vmx Duplex socket: 3
2025-06-12T13:54:52.572Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 9
2025-06-12T13:54:52.572Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 11
2025-06-12T13:54:52.572Z In(05) vmx VigorTransport listening on fd 12
2025-06-12T13:54:52.572Z In(05) vmx Vigor_Init 1
2025-06-12T13:54:52.572Z In(05) vmx Connecting 'ui' to fd '3' with user '(null)'
2025-06-12T13:54:52.572Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2025-06-12T13:54:52.602Z In(05) vmx /mnt/vm/vm_lonele/Windows7_Clone_Simple.vmx: Setup symlink /var/run/vmware/67fda5ca9e44484e0d5483508e3ff968f31e3e0c74ac358b75aae8c7e849f4b5 -> /var/run/vmware/1000/**********_42376
2025-06-12T13:54:52.603Z In(05) vmx Vix: [mainDispatch.c:488]: VMAutomation: Initializing VMAutomation.
2025-06-12T13:54:52.603Z In(05) vmx Vix: [mainDispatch.c:740]: VMAutomationOpenListenerSocket() listening
2025-06-12T13:54:52.604Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-06-12T13:54:52.604Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-06-12T13:54:52.604Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-06-12T13:54:52.604Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2025-06-12T13:54:52.604Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2025-06-12T13:54:52.605Z In(05) vmx FeatureCompat: No EVC masks.
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID vendor: GenuineIntel
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID family: 0x6 model: 0x97 stepping: 0x2
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID codename: Alder Lake-S
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID name: 12th Gen Intel(R) Core(TM) i7-12800HX
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000000, 0: 0x00000020 0x756e6547 0x6c65746e 0x49656e69
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000001, 0: 0x00090672 0x30800800 0x7ffafbbf 0xbfebfbff
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000002, 0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000004, 0: 0xfc004121 0x02c0003f 0x0000003f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000004, 1: 0xfc004122 0x01c0003f 0x0000003f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000004, 2: 0xfc01c143 0x0240003f 0x000007ff 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000004, 3: 0xfc1fc163 0x0240003f 0x00009fff 0x00000004
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x10102020
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00060003
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000007, 0: 0x00000002 0x239c27eb 0x98c007bc 0xfc18c410
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000007, 1: 0x00400810 0x00000000 0x00000000 0x00040000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000007, 2: 0x00000000 0x00000000 0x00000000 0x00000097
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000a, 0: 0x07300605 0x00000000 0x00000007 0x00008603
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000030
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000b, 1: 0x00000007 0x00000018 0x00000201 0x00000030
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000030
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, 0: 0x00000207 0x00000a88 0x00000a88 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, 1: 0x0000000f 0x00000680 0x00019900 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, 5: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, 6: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, 7: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, 8: 0x00000080 0x00000000 0x00000001 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, 9: 0x00000008 0x00000a80 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, a: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, b: 0x00000010 0x00000000 0x00000001 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, c: 0x00000018 0x00000000 0x00000001 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, d: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, e: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, f: 0x00000328 0x00000000 0x00000001 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000d, 10: 0x00000008 0x00000000 0x00000001 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000010, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000012, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000012, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000012, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000014, 0: 0x00000001 0x0000005f 0x00000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000014, 1: 0x02490002 0x003f003f 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000015, 0: 0x00000002 0x00000078 0x0249f000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000016, 0: 0x000008fc 0x000012c0 0x00000064 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000017, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000018, 0: 0x00000008 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000018, 1: 0x00000000 0x00080001 0x00000020 0x00004022
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000018, 2: 0x00000000 0x00080006 0x00000004 0x00004022
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000018, 3: 0x00000000 0x0010000f 0x00000001 0x00004125
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000018, 4: 0x00000000 0x00040001 0x00000010 0x00004024
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000018, 5: 0x00000000 0x00040006 0x00000008 0x00004024
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000018, 6: 0x00000000 0x00080008 0x00000001 0x00004124
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000018, 7: 0x00000000 0x00080007 0x00000080 0x00004043
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000018, 8: 0x00000000 0x00080009 0x00000080 0x00004043
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000019, 0: 0x00000007 0x00000014 0x00000003 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000001a, 0: 0x40000001 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000001b, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000001c, 0: 0x4000000b 0x00000007 0x00000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000001d, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000001e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000001f, 0: 0x00000001 0x00000002 0x00000100 0x00000030
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000001f, 1: 0x00000007 0x00000018 0x00000201 0x00000030
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 0000001f, 2: 0x00000000 0x00000000 0x00000002 0x00000030
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 00000020, 0: 0x00000000 0x00000001 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 80000002, 0: 0x68743231 0x6e654720 0x746e4920 0x52286c65
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 80000003, 0: 0x6f432029 0x54286572 0x6920294d 0x32312d37
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 80000004, 0: 0x48303038 0x00000058 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x05007040 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-06-12T13:54:52.606Z In(05) vmx hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID differences from hostCPUID.
2025-06-12T13:54:52.606Z In(05) vmx CPUID[1] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00040003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[1] level 00000018, 0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[1] level 00000018, 1: 0x00000000 0x00300001 0x00000001 0x00000121
2025-06-12T13:54:52.606Z In(05) vmx CPUID[1] level 00000018, 2: 0x00000000 0x00040003 0x00000200 0x00000043
2025-06-12T13:54:52.606Z In(05) vmx CPUID[1] level 00000018, 3: 0x00000000 0x00400001 0x00000001 0x00000122
2025-06-12T13:54:52.606Z In(05) vmx CPUID[1] level 00000018, 4: 0x00000000 0x00080008 0x00000001 0x00000143
2025-06-12T13:54:52.606Z In(05) vmx CPUID[1] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[1] level 0000001f, 0: 0x00000001 0x00000001 0x00000100 0x0000004c
2025-06-12T13:54:52.606Z In(05) vmx CPUID[2] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00040003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[2] level 00000016, 0: 0x000008fc 0x00000d48 0x00000064 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[2] level 0000001c, 0: 0xc000000b 0x00000007 0x00000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[3] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00070003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[3] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[3] level 00000018, 0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[3] level 00000018, 1: 0x00000000 0x00300001 0x00000001 0x00000121
2025-06-12T13:54:52.606Z In(05) vmx CPUID[3] level 00000018, 2: 0x00000000 0x00040003 0x00000200 0x00000043
2025-06-12T13:54:52.606Z In(05) vmx CPUID[3] level 00000018, 3: 0x00000000 0x00400001 0x00000001 0x00000122
2025-06-12T13:54:52.606Z In(05) vmx CPUID[3] level 00000018, 4: 0x00000000 0x00080008 0x00000001 0x00000143
2025-06-12T13:54:52.606Z In(05) vmx CPUID[3] level 0000001f, 0: 0x00000001 0x00000001 0x00000100 0x00000042
2025-06-12T13:54:52.606Z In(05) vmx CPUID[3] level 80000006, 0: 0x00000000 0x00000000 0x08008040 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[4] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00080003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[4] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[4] level 0000001c, 0: 0xc000000b 0x00000007 0x00000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[5] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00090003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[5] level 0000000b, 0: 0x00000001 0x00000001 0x00000100 0x00000042
2025-06-12T13:54:52.606Z In(05) vmx CPUID[5] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[5] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 00000004, 0: 0xfc004121 0x01c0003f 0x0000003f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 00000004, 1: 0xfc004122 0x01c0003f 0x0000007f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 00000004, 2: 0xfc01c143 0x03c0003f 0x000007ff 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00020003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 00000016, 0: 0x000008fc 0x00000d48 0x00000064 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 00000018, 0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 00000018, 1: 0x00000000 0x00300001 0x00000001 0x00000121
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 00000018, 2: 0x00000000 0x00040003 0x00000200 0x00000043
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 00000018, 3: 0x00000000 0x00400001 0x00000001 0x00000122
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 00000018, 4: 0x00000000 0x00080008 0x00000001 0x00000143
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 0000001c, 0: 0xc000000b 0x00000007 0x00000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[6] level 80000006, 0: 0x00000000 0x00000000 0x08008040 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[7] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00010003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[8] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00010003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[8] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[9] level 00000002, 0: 0x00feff01 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[9] level 00000004, 0: 0xfc004121 0x01c0003f 0x0000003f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[9] level 00000004, 1: 0xfc004122 0x01c0003f 0x0000007f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[9] level 00000004, 2: 0xfc01c143 0x03c0003f 0x000007ff 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[9] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00000003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[9] level 0000000b, 0: 0x00000001 0x00000001 0x00000100 0x0000004a
2025-06-12T13:54:52.606Z In(05) vmx CPUID[9] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[9] level 00000016, 0: 0x000008fc 0x00000d48 0x00000064 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[9] level 80000006, 0: 0x00000000 0x00000000 0x08008040 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[10] level 00000002, 0: 0x00feff01 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[10] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00030003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[10] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[11] level 0000001f, 0: 0x00000001 0x00000001 0x00000100 0x00000048
2025-06-12T13:54:52.606Z In(05) vmx CPUID[12] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00030003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[13] level 00000004, 0: 0xfc004121 0x01c0003f 0x0000003f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[13] level 00000004, 1: 0xfc004122 0x01c0003f 0x0000007f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[13] level 00000004, 2: 0xfc01c143 0x03c0003f 0x000007ff 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[13] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00050003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[13] level 00000016, 0: 0x000008fc 0x00000d48 0x00000064 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[13] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[13] level 0000001f, 0: 0x00000001 0x00000001 0x00000100 0x0000004e
2025-06-12T13:54:52.606Z In(05) vmx CPUID[14] level 00000002, 0: 0x00feff01 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[14] level 00000004, 0: 0xfc004121 0x01c0003f 0x0000003f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[14] level 00000004, 1: 0xfc004122 0x01c0003f 0x0000007f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[14] level 00000004, 2: 0xfc01c143 0x03c0003f 0x000007ff 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[14] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00020003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[14] level 0000001f, 0: 0x00000001 0x00000001 0x00000100 0x0000004a
2025-06-12T13:54:52.606Z In(05) vmx CPUID[15] level 00000002, 0: 0x00feff01 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[15] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00090003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[15] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[15] level 0000001c, 0: 0xc000000b 0x00000007 0x00000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[15] level 0000001f, 0: 0x00000001 0x00000001 0x00000100 0x00000040
2025-06-12T13:54:52.606Z In(05) vmx CPUID[15] level 80000006, 0: 0x00000000 0x00000000 0x08008040 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[16] level 00000002, 0: 0x00feff01 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[16] level 00000004, 0: 0xfc004121 0x01c0003f 0x0000003f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[16] level 00000004, 1: 0xfc004122 0x01c0003f 0x0000007f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[16] level 00000004, 2: 0xfc01c143 0x03c0003f 0x000007ff 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[16] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00050003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[16] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[16] level 00000016, 0: 0x000008fc 0x00000d48 0x00000064 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[16] level 0000001c, 0: 0xc000000b 0x00000007 0x00000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[16] level 0000001f, 0: 0x00000001 0x00000001 0x00000100 0x00000044
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 00000002, 0: 0x00feff01 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 00000004, 0: 0xfc004121 0x01c0003f 0x0000003f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 00000004, 1: 0xfc004122 0x01c0003f 0x0000007f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 00000004, 2: 0xfc01c143 0x03c0003f 0x000007ff 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00090003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 0000000b, 0: 0x00000001 0x00000001 0x00000100 0x0000004c
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 00000018, 0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 00000018, 1: 0x00000000 0x00300001 0x00000001 0x00000121
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 00000018, 2: 0x00000000 0x00040003 0x00000200 0x00000043
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 00000018, 3: 0x00000000 0x00400001 0x00000001 0x00000122
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 00000018, 4: 0x00000000 0x00080008 0x00000001 0x00000143
2025-06-12T13:54:52.606Z In(05) vmx CPUID[17] level 0000001c, 0: 0xc000000b 0x00000007 0x00000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000002, 0: 0x00feff01 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000004, 0: 0xfc004121 0x01c0003f 0x0000003f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000004, 1: 0xfc004122 0x01c0003f 0x0000007f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000004, 2: 0xfc01c143 0x03c0003f 0x000007ff 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00070003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 0000000b, 0: 0x00000001 0x00000001 0x00000100 0x00000040
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000016, 0: 0x000008fc 0x00000d48 0x00000064 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000018, 0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000018, 1: 0x00000000 0x00300001 0x00000001 0x00000121
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000018, 2: 0x00000000 0x00040003 0x00000200 0x00000043
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000018, 3: 0x00000000 0x00400001 0x00000001 0x00000122
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 00000018, 4: 0x00000000 0x00080008 0x00000001 0x00000143
2025-06-12T13:54:52.606Z In(05) vmx CPUID[18] level 80000006, 0: 0x00000000 0x00000000 0x08008040 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 00000002, 0: 0x00feff01 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 00000004, 0: 0xfc004121 0x01c0003f 0x0000003f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 00000004, 1: 0xfc004122 0x01c0003f 0x0000007f 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 00000004, 2: 0xfc01c143 0x03c0003f 0x000007ff 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00080003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 0000000b, 0: 0x00000001 0x00000001 0x00000100 0x0000004e
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 00000018, 0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 00000018, 1: 0x00000000 0x00300001 0x00000001 0x00000121
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 00000018, 2: 0x00000000 0x00040003 0x00000200 0x00000043
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 00000018, 3: 0x00000000 0x00400001 0x00000001 0x00000122
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 00000018, 4: 0x00000000 0x00080008 0x00000001 0x00000143
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[19] level 80000006, 0: 0x00000000 0x00000000 0x08008040 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[20] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00090003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[20] level 0000000b, 0: 0x00000001 0x00000001 0x00000100 0x00000044
2025-06-12T13:54:52.606Z In(05) vmx CPUID[20] level 00000016, 0: 0x000008fc 0x00000d48 0x00000064 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[20] level 00000018, 0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[20] level 00000018, 1: 0x00000000 0x00300001 0x00000001 0x00000121
2025-06-12T13:54:52.606Z In(05) vmx CPUID[20] level 00000018, 2: 0x00000000 0x00040003 0x00000200 0x00000043
2025-06-12T13:54:52.606Z In(05) vmx CPUID[20] level 00000018, 3: 0x00000000 0x00400001 0x00000001 0x00000122
2025-06-12T13:54:52.606Z In(05) vmx CPUID[20] level 00000018, 4: 0x00000000 0x00080008 0x00000001 0x00000143
2025-06-12T13:54:52.606Z In(05) vmx CPUID[20] level 0000001c, 0: 0xc000000b 0x00000007 0x00000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[21] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00080003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[21] level 0000000b, 0: 0x00000001 0x00000001 0x00000100 0x00000046
2025-06-12T13:54:52.606Z In(05) vmx CPUID[21] level 00000016, 0: 0x000008fc 0x00000d48 0x00000064 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[21] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[21] level 0000001f, 0: 0x00000001 0x00000001 0x00000100 0x00000046
2025-06-12T13:54:52.606Z In(05) vmx CPUID[21] level 80000006, 0: 0x00000000 0x00000000 0x08008040 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[22] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00080003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[22] level 0000001c, 0: 0xc000000b 0x00000007 0x00000007 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[22] level 80000006, 0: 0x00000000 0x00000000 0x08008040 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[23] level 00000006, 0: 0x00dfcff7 0x00000002 0x00000409 0x00000003
2025-06-12T13:54:52.606Z In(05) vmx CPUID[23] level 0000000b, 0: 0x00000001 0x00000001 0x00000100 0x00000048
2025-06-12T13:54:52.606Z In(05) vmx CPUID[23] level 00000018, 0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx CPUID[23] level 00000018, 1: 0x00000000 0x00300001 0x00000001 0x00000121
2025-06-12T13:54:52.606Z In(05) vmx CPUID[23] level 00000018, 2: 0x00000000 0x00040003 0x00000200 0x00000043
2025-06-12T13:54:52.606Z In(05) vmx CPUID[23] level 00000018, 3: 0x00000000 0x00400001 0x00000001 0x00000122
2025-06-12T13:54:52.606Z In(05) vmx CPUID[23] level 00000018, 4: 0x00000000 0x00080008 0x00000001 0x00000143
2025-06-12T13:54:52.606Z In(05) vmx CPUID[23] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.606Z In(05) vmx Physical APIC IDs: 40,78,66,41,49,56,16,32,8,0,74,1,48,72,33,64,24,9,70,17,68,25,57,76
2025-06-12T13:54:52.606Z In(05) vmx Physical X2APIC IDs: 0-1,8-9,16-17,24-25,32-33,40-41,48-49,56-57,64,66,68,70,72,74,76,78
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR       0x3a =                0x5
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x480 =  0x3da050000000013
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x481 =       0xff00000016
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x482 = 0xfffbfffe0401e172
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x483 = 0xf77fffff00036dff
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x484 =   0x76ffff000011ff
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x485 =         0x7004c1e7
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x486 =         0x80000021
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x487 =         0xffffffff
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x488 =             0x2000
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x489 =          0x1ff2fff
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x48a =               0x2e
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x48b =  0x75d7fff00000000
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x48c =      0xf0106f34141
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x48d =       0xff00000016
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x48e = 0xfffbfffe04006172
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x48f = 0xf77fffff00036dfb
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x490 =   0x76ffff000011fb
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x491 =                0x1
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x492 =                0x1
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR 0xc0010114 =                  0
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR       0xce =         0x80000000
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x10a =         0x14c8fd6b
2025-06-12T13:54:52.606Z In(05) vmx Common: MSR      0x122 =                  0
2025-06-12T13:54:52.606Z In(05) vmx UTIL: Current file descriptor limit: soft 16384, hard 1048576.
2025-06-12T13:54:52.607Z In(05) vmx VMMon_GetkHzEstimate: Calculated 2304000 kHz
2025-06-12T13:54:52.607Z In(05) vmx TSC Hz estimates: vmmon 2304000000, cpuinfo 3822755000, cpufreq 4800000000 sysctlfreq 0. Using 2304000000 Hz
2025-06-12T13:54:52.607Z In(05) vmx PTSC: Host is using tsc as clocksource.
2025-06-12T13:54:52.607Z In(05) vmx PTSC: RefClockToPTSC 0 @ 1000000Hz -> 0 @ 2304000000Hz
2025-06-12T13:54:52.607Z In(05) vmx PTSC: RefClockToPTSC ((x * 2415919104) >> 20) + -7601296414464
2025-06-12T13:54:52.607Z In(05) vmx PTSC: tscOffset -7681576448995
2025-06-12T13:54:52.607Z In(05) vmx PTSC: using TSC
2025-06-12T13:54:52.607Z In(05) vmx PTSC: hardware TSCs are synchronized.
2025-06-12T13:54:52.607Z In(05) vmx PTSC: current PTSC=22276
2025-06-12T13:54:52.607Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 17
2025-06-12T13:54:52.607Z In(05) vmx MKSXlib: Initialized thread-safe Xlib
2025-06-12T13:54:52.608Z In(05) vmx [0x56D3B73D4000-0x56D3B7522AC0): /usr/lib/vmware/bin/vmware-vmx
2025-06-12T13:54:52.621Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2025-06-12T13:54:52.621Z In(05) vmx changing directory to /mnt/vm/vm_lonele/.
2025-06-12T13:54:52.621Z In(05) vmx Config file: /mnt/vm/vm_lonele/Windows7_Clone_Simple.vmx
2025-06-12T13:54:52.621Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-06-12T13:54:52.621Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2025-06-12T13:54:52.622Z In(05) vmx LogRotation: Rotating to a new log file (keepOld 3) took 0.000346 seconds.
2025-06-12T13:54:52.622Z No(00) vmx LogVMXReplace: Successful switching from temporary to permanent log file took 0.001114 seconds.
2025-06-12T13:54:52.643Z Wa(03) vmx PowerOn
2025-06-12T13:54:52.643Z In(05) vmx VMX_PowerOn: VMX build 23775571, UI build 23775571
2025-06-12T13:54:52.643Z In(05) vmx libnuma.so found and in use.
2025-06-12T13:54:52.645Z In(05) vmx NUMA topology found.
2025-06-12T13:54:52.646Z In(05) vmx NUMA node 0: 47902MB, cpus 0x00ffffff 0x00000000 0x00000000 0x00000000
2025-06-12T13:54:52.648Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2025-06-12T13:54:52.649Z In(05) vmx VMXSTATS: Successfully created stats file 'Windows7_Clone_Simple.scoreboard'
2025-06-12T13:54:52.649Z In(05) vmx VMXSTATS: Update Product Information: VMware Workstation	17.5.2	build-23775571	Release  TotalBlockSize: 56
2025-06-12T13:54:52.649Z In(05) vmx HOST sysname Linux, nodename geyee-jiguangxseriesgm6aq7c, release 6.8.0-60-generic, version #63-Ubuntu SMP PREEMPT_DYNAMIC Tue Apr 15 19:04:15 UTC 2025, machine x86_64
2025-06-12T13:54:52.649Z In(05) vmx DICT --- GLOBAL SETTINGS /usr/lib/vmware/settings
2025-06-12T13:54:52.649Z In(05) vmx DICT --- NON PERSISTENT (null)
2025-06-12T13:54:52.650Z In(05) vmx DICT --- USER PREFERENCES /home/<USER>/.vmware/preferences
2025-06-12T13:54:52.650Z In(05) vmx DICT   pref.lastUpdateCheckSec = "1749733291"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.wspro.firstRunDismissedVersion = "17.5.2"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window.count = "2"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.tab.count = "4"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.tab0.cnxType = "vmdb"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.tab1.cnxType = "vmdb"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.sidebar = "TRUE"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.sidebar.width = "209"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.statusBar = "TRUE"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.tabs = "TRUE"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar = "FALSE"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.size = "125"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.view = "same-folder"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.placement.left = "0"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.placement.top = "0"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.placement.right = "2560"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.placement.bottom = "1528"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.maximized = "FALSE"
2025-06-12T13:54:52.650Z In(05) vmx DICT       pref.license.maxNum = "1"
2025-06-12T13:54:52.650Z In(05) vmx DICT     pref.license0.version = "17.0"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.license0.registrationViewed = "FALSE"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.license0.lastEvalReminder = "0"
2025-06-12T13:54:52.650Z In(05) vmx DICT pref.ws.session.window0.tab0.dest = ""
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.ws.session.window0.tab0.file = "home"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.ws.session.window0.tab0.type = "home"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.ws.session.window0.tab0.focused = "FALSE"
2025-06-12T13:54:52.651Z In(05) vmx DICT       pref.usbDev.maxDevs = "2"
2025-06-12T13:54:52.651Z In(05) vmx DICT     pref.usbDev0.deviceID = "04f2-b78a-200901010001"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.usbDev0.showInStatusbar = "FALSE"
2025-06-12T13:54:52.651Z In(05) vmx DICT       pref.usbDev0.iconID = "0"
2025-06-12T13:54:52.651Z In(05) vmx DICT     pref.usbDev1.deviceID = "1152921511058145318"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.usbDev1.showInStatusbar = "FALSE"
2025-06-12T13:54:52.651Z In(05) vmx DICT       pref.usbDev1.iconID = "0"
2025-06-12T13:54:52.651Z In(05) vmx DICT vmWizard.isoLocationMRU.count = "1"
2025-06-12T13:54:52.651Z In(05) vmx DICT vmWizard.isoLocationMRU0.location = "/home/<USER>/Desktop/Hikari_PE_X64_V7.1_TC_202001092215.iso"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.ws.session.window0.tab2.cnxType = "vmdb"
2025-06-12T13:54:52.651Z In(05) vmx DICT  pref.sharedFolder.maxNum = "5"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder0.vmPath = "/vm/#0634c16226caf7fb/"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder0.guestName = "desk"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder0.hostPath = "/home/<USER>/Desktop"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder0.enabled = "TRUE"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder1.vmPath = "/vm/#0634c16226caf7fb/"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder1.guestName = "share"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder1.hostPath = "/usr/share/"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder1.enabled = "TRUE"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder2.vmPath = "/vm/#0634c16226caf7fb/"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder2.guestName = "share"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder2.hostPath = "/usr/share/com_ui"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.sharedFolder2.enabled = "TRUE"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.ws.session.window0.tab1.dest = ""
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.ws.session.window0.tab1.file = "/home/<USER>/vmware/Windows 7/Windows 7.vmx"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.ws.session.window0.tab1.type = "vm"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.ws.session.window0.tab1.focused = "FALSE"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.ws.session.window1.tab0.cnxType = "vmdb"
2025-06-12T13:54:52.651Z In(05) vmx DICT  pref.fullscreen.autohide = "TRUE"
2025-06-12T13:54:52.651Z In(05) vmx DICT pref.remoteMKSHardwareAccel = "TRUE"
2025-06-12T13:54:52.651Z In(05) vmx DICT             hints.hideAll = "FALSE"
2025-06-12T13:54:52.652Z In(05) vmx DICT hint.usb.disconnectHostDriver = "FALSE"
2025-06-12T13:54:52.652Z In(05) vmx DICT  hint.vmui.showAllUSBDevs = "FALSE"
2025-06-12T13:54:52.652Z In(05) vmx DICT         vmWizard.guestKey = "windows7"
2025-06-12T13:54:52.652Z In(05) vmx DICT vmWizard.installMediaType = "later"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window0.tab3.cnxType = "vmdb"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tab.count = "2"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tab0.dest = ""
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tab0.file = "home"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tab0.type = "home"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tab0.focused = "TRUE"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tab1.dest = ""
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tab1.file = "folder:localhost"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tab1.type = "folder"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tab1.cnxType = "vmdb"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tab1.focused = "FALSE"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tab2.cnxType = "vmdb"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.sidebar = "TRUE"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.sidebar.width = "209"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.statusBar = "TRUE"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.tabs = "TRUE"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.thumbnailBar = "FALSE"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.thumbnailBar.size = "125"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.thumbnailBar.view = "same-folder"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.placement.left = "0"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.placement.top = "22"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.placement.right = "2560"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.placement.bottom = "1550"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window1.maximized = "FALSE"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.sharedFolder3.vmPath = "/vm/#9731f83dbd8df423/"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.sharedFolder3.guestName = "D"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.sharedFolder3.hostPath = "D:\"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.sharedFolder3.enabled = "FALSE"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.sharedFolder4.vmPath = "/vm/#9731f83dbd8df423/"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.sharedFolder4.guestName = "G"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.sharedFolder4.hostPath = "G:\"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.sharedFolder4.enabled = "FALSE"
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window0.tab2.dest = ""
2025-06-12T13:54:52.652Z In(05) vmx DICT pref.ws.session.window0.tab2.file = "/mnt/vm/win10x64/Windows 10 x64.vmx"
2025-06-12T13:54:52.653Z In(05) vmx DICT pref.ws.session.window0.tab2.type = "vm"
2025-06-12T13:54:52.653Z In(05) vmx DICT pref.ws.session.window0.tab2.focused = "FALSE"
2025-06-12T13:54:52.653Z In(05) vmx DICT pref.ws.session.window0.tab3.dest = ""
2025-06-12T13:54:52.653Z In(05) vmx DICT pref.ws.session.window0.tab3.file = "/mnt/vm/vm_lonele/Windows7_Clone_Simple.vmx"
2025-06-12T13:54:52.653Z In(05) vmx DICT pref.ws.session.window0.tab3.type = "vm"
2025-06-12T13:54:52.653Z In(05) vmx DICT pref.ws.session.window0.tab3.focused = "TRUE"
2025-06-12T13:54:52.653Z In(05) vmx DICT --- USER DEFAULTS /home/<USER>/.vmware/config
2025-06-12T13:54:52.653Z In(05) vmx DICT --- HOST DEFAULTS /etc/vmware/config
2025-06-12T13:54:52.653Z In(05) vmx DICT             telemetryUUID = "98eb13b7-6889-11ef-9d4a-b025aa70d1d8"
2025-06-12T13:54:52.653Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "1531716773"
2025-06-12T13:54:52.653Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "4870639455"
2025-06-12T13:54:52.653Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-06-12T13:54:52.653Z In(05) vmx DICT installerDefaults.transferVersion = "1"
2025-06-12T13:54:52.653Z In(05) vmx DICT              product.name = "VMware Workstation"
2025-06-12T13:54:52.653Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "no"
2025-06-12T13:54:52.653Z In(05) vmx DICT                    libdir = "/usr/lib/vmware"
2025-06-12T13:54:52.653Z In(05) vmx DICT                    bindir = "/usr/bin"
2025-06-12T13:54:52.653Z In(05) vmx DICT                   initdir = "/etc"
2025-06-12T13:54:52.653Z In(05) vmx DICT             initscriptdir = "/etc/init.d"
2025-06-12T13:54:52.653Z In(05) vmx DICT           gksu.rootMethod = "sudo"
2025-06-12T13:54:52.653Z In(05) vmx DICT                NETWORKING = "yes"
2025-06-12T13:54:52.653Z In(05) vmx DICT            authd.fullpath = "/usr/sbin/vmware-authd"
2025-06-12T13:54:52.653Z In(05) vmx DICT       product.buildNumber = "23775571"
2025-06-12T13:54:52.653Z In(05) vmx DICT    player.product.version = "17.5.2"
2025-06-12T13:54:52.653Z In(05) vmx DICT        vix.config.version = "1"
2025-06-12T13:54:52.653Z In(05) vmx DICT           vmware.fullpath = "/usr/bin/vmware"
2025-06-12T13:54:52.653Z In(05) vmx DICT                vix.libdir = "/usr/lib/vmware-vix"
2025-06-12T13:54:52.653Z In(05) vmx DICT           product.version = "17.5.2"
2025-06-12T13:54:52.653Z In(05) vmx DICT workstation.product.version = "17.5.2"
2025-06-12T13:54:52.653Z In(05) vmx DICT                acceptEULA = "yes"
2025-06-12T13:54:52.653Z In(05) vmx DICT             acceptOVFEULA = "yes"
2025-06-12T13:54:52.653Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "no"
2025-06-12T13:54:52.653Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.initialized = "yes"
2025-06-12T13:54:52.653Z In(05) vmx DICT --- SITE DEFAULTS /usr/lib/vmware/config
2025-06-12T13:54:52.653Z In(05) vmx DICT                  tag.help = "introduction.htm"
2025-06-12T13:54:52.653Z In(05) vmx DICT   tag.configurationEditor = "config_editor_newvm.htm"
2025-06-12T13:54:52.653Z In(05) vmx DICT             tag.ideConfig = "devices_virtualdrive.htm"
2025-06-12T13:54:52.653Z In(05) vmx DICT          tag.floppyConfig = "devices_floppy.htm"
2025-06-12T13:54:52.653Z In(05) vmx DICT           tag.mouseConfig = "devices_mouse.htm"
2025-06-12T13:54:52.653Z In(05) vmx DICT             tag.netConfig = "devices_netadapter.htm"
2025-06-12T13:54:52.653Z In(05) vmx DICT        tag.parallelConfig = "devices_parallel.htm"
2025-06-12T13:54:52.654Z In(05) vmx DICT          tag.serialConfig = "devices_serial.htm"
2025-06-12T13:54:52.654Z In(05) vmx DICT           tag.soundConfig = "devices_sound.htm"
2025-06-12T13:54:52.654Z In(05) vmx DICT             tag.memConfig = "configvm_memory.htm"
2025-06-12T13:54:52.654Z In(05) vmx DICT            tag.miscConfig = "configvm.htm"
2025-06-12T13:54:52.654Z In(05) vmx DICT             tag.usbConfig = "devices_usb.htm"
2025-06-12T13:54:52.654Z In(05) vmx DICT         tag.displayConfig = "configvm_display-problems.htm"
2025-06-12T13:54:52.654Z In(05) vmx DICT                 tag.tools = "vmtools.htm"
2025-06-12T13:54:52.654Z In(05) vmx DICT --- NONPERSISTENT
2025-06-12T13:54:52.654Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT             gui.available = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT --- COMMAND LINE
2025-06-12T13:54:52.654Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT             gui.available = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT --- RECORDING
2025-06-12T13:54:52.654Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT             gui.available = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT --- CONFIGURATION /mnt/vm/vm_lonele/Windows7_Clone_Simple.vmx 
2025-06-12T13:54:52.654Z In(05) vmx DICT            config.version = "8"
2025-06-12T13:54:52.654Z In(05) vmx DICT         virtualHW.version = "19"
2025-06-12T13:54:52.654Z In(05) vmx DICT               displayName = "Windows 7 Clone"
2025-06-12T13:54:52.654Z In(05) vmx DICT                   guestOS = "windows7"
2025-06-12T13:54:52.654Z In(05) vmx DICT                  numvcpus = "2"
2025-06-12T13:54:52.654Z In(05) vmx DICT                   memsize = "2048"
2025-06-12T13:54:52.654Z In(05) vmx DICT             scsi0.present = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT          scsi0.virtualDev = "lsilogic"
2025-06-12T13:54:52.654Z In(05) vmx DICT           scsi0:0.present = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT        scsi0:0.deviceType = "disk"
2025-06-12T13:54:52.654Z In(05) vmx DICT          scsi0:0.fileName = "Windows 7-cl1-000002.vmdk"
2025-06-12T13:54:52.654Z In(05) vmx DICT            ide1:0.present = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT         ide1:0.deviceType = "cdrom-image"
2025-06-12T13:54:52.654Z In(05) vmx DICT     ide1:0.startConnected = "FALSE"
2025-06-12T13:54:52.654Z In(05) vmx DICT         ethernet0.present = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT      ethernet0.virtualDev = "e1000"
2025-06-12T13:54:52.654Z In(05) vmx DICT     ethernet0.networkName = "NAT"
2025-06-12T13:54:52.654Z In(05) vmx DICT     ethernet0.addressType = "generated"
2025-06-12T13:54:52.654Z In(05) vmx DICT             sound.present = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT          sound.virtualDev = "hdaudio"
2025-06-12T13:54:52.654Z In(05) vmx DICT               usb.present = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT              ehci.present = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2025-06-12T13:54:52.654Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2025-06-12T13:54:52.654Z In(05) vmx DICT      pciBridge4.functions = "8"
2025-06-12T13:54:52.654Z In(05) vmx DICT             vmci0.present = "TRUE"
2025-06-12T13:54:52.655Z In(05) vmx DICT             hpet0.present = "TRUE"
2025-06-12T13:54:52.655Z In(05) vmx DICT        powerType.powerOff = "soft"
2025-06-12T13:54:52.655Z In(05) vmx DICT         powerType.powerOn = "soft"
2025-06-12T13:54:52.655Z In(05) vmx DICT         powerType.suspend = "soft"
2025-06-12T13:54:52.655Z In(05) vmx DICT           powerType.reset = "soft"
2025-06-12T13:54:52.655Z In(05) vmx DICT             cleanShutdown = "TRUE"
2025-06-12T13:54:52.655Z In(05) vmx DICT        extendedConfigFile = "Windows7_Clone_Simple.vmxf"
2025-06-12T13:54:52.655Z In(05) vmx DICT virtualHW.productCompatibility = "hosted"
2025-06-12T13:54:52.655Z In(05) vmx DICT            fileSearchPath = "/mnt/vm/vm_lonele;."
2025-06-12T13:54:52.655Z In(05) vmx DICT         vmxstats.filename = "Windows7_Clone_Simple.scoreboard"
2025-06-12T13:54:52.655Z In(05) vmx DICT      numa.autosize.cookie = "20012"
2025-06-12T13:54:52.655Z In(05) vmx DICT numa.autosize.vcpu.maxPerVirtualNode = "2"
2025-06-12T13:54:52.655Z In(05) vmx DICT                 uuid.bios = "56 4d ce 07 2c 9c 7a 53-4e 18 a8 c3 1e d7 f0 00"
2025-06-12T13:54:52.655Z In(05) vmx DICT             uuid.location = "56 4d ce 07 2c 9c 7a 53-4e 18 a8 c3 1e d7 f0 00"
2025-06-12T13:54:52.655Z In(05) vmx DICT  pciBridge0.pciSlotNumber = "17"
2025-06-12T13:54:52.655Z In(05) vmx DICT  pciBridge4.pciSlotNumber = "21"
2025-06-12T13:54:52.655Z In(05) vmx DICT       scsi0.pciSlotNumber = "16"
2025-06-12T13:54:52.655Z In(05) vmx DICT         usb.pciSlotNumber = "32"
2025-06-12T13:54:52.655Z In(05) vmx DICT   ethernet0.pciSlotNumber = "33"
2025-06-12T13:54:52.655Z In(05) vmx DICT       sound.pciSlotNumber = "34"
2025-06-12T13:54:52.655Z In(05) vmx DICT        ehci.pciSlotNumber = "35"
2025-06-12T13:54:52.655Z In(05) vmx DICT --- USER DEFAULTS ~/.vmware/config 
2025-06-12T13:54:52.655Z In(05) vmx DICT --- HOST DEFAULTS /etc/vmware/config 
2025-06-12T13:54:52.655Z In(05) vmx DICT             telemetryUUID = "98eb13b7-6889-11ef-9d4a-b025aa70d1d8"
2025-06-12T13:54:52.655Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "1531716773"
2025-06-12T13:54:52.655Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "4870639455"
2025-06-12T13:54:52.655Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-06-12T13:54:52.655Z In(05) vmx DICT installerDefaults.transferVersion = "1"
2025-06-12T13:54:52.655Z In(05) vmx DICT              product.name = "VMware Workstation"
2025-06-12T13:54:52.655Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "no"
2025-06-12T13:54:52.655Z In(05) vmx DICT                    libdir = "/usr/lib/vmware"
2025-06-12T13:54:52.655Z In(05) vmx DICT                    bindir = "/usr/bin"
2025-06-12T13:54:52.655Z In(05) vmx DICT                   initdir = "/etc"
2025-06-12T13:54:52.655Z In(05) vmx DICT             initscriptdir = "/etc/init.d"
2025-06-12T13:54:52.655Z In(05) vmx DICT           gksu.rootMethod = "sudo"
2025-06-12T13:54:52.655Z In(05) vmx DICT                NETWORKING = "yes"
2025-06-12T13:54:52.655Z In(05) vmx DICT            authd.fullpath = "/usr/sbin/vmware-authd"
2025-06-12T13:54:52.656Z In(05) vmx DICT       product.buildNumber = "23775571"
2025-06-12T13:54:52.656Z In(05) vmx DICT    player.product.version = "17.5.2"
2025-06-12T13:54:52.656Z In(05) vmx DICT        vix.config.version = "1"
2025-06-12T13:54:52.656Z In(05) vmx DICT           vmware.fullpath = "/usr/bin/vmware"
2025-06-12T13:54:52.656Z In(05) vmx DICT                vix.libdir = "/usr/lib/vmware-vix"
2025-06-12T13:54:52.656Z In(05) vmx DICT           product.version = "17.5.2"
2025-06-12T13:54:52.656Z In(05) vmx DICT workstation.product.version = "17.5.2"
2025-06-12T13:54:52.656Z In(05) vmx DICT                acceptEULA = "yes"
2025-06-12T13:54:52.656Z In(05) vmx DICT             acceptOVFEULA = "yes"
2025-06-12T13:54:52.656Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "no"
2025-06-12T13:54:52.656Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.initialized = "yes"
2025-06-12T13:54:52.656Z In(05) vmx DICT --- SITE DEFAULTS /usr/lib/vmware/config 
2025-06-12T13:54:52.656Z In(05) vmx DICT                  tag.help = "introduction.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT   tag.configurationEditor = "config_editor_newvm.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT             tag.ideConfig = "devices_virtualdrive.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT          tag.floppyConfig = "devices_floppy.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT           tag.mouseConfig = "devices_mouse.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT             tag.netConfig = "devices_netadapter.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT        tag.parallelConfig = "devices_parallel.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT          tag.serialConfig = "devices_serial.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT           tag.soundConfig = "devices_sound.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT             tag.memConfig = "configvm_memory.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT            tag.miscConfig = "configvm.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT             tag.usbConfig = "devices_usb.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT         tag.displayConfig = "configvm_display-problems.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT                 tag.tools = "vmtools.htm"
2025-06-12T13:54:52.656Z In(05) vmx DICT --- GLOBAL SETTINGS /usr/lib/vmware/settings 
2025-06-12T13:54:52.656Z In(05) vmx VMXSTATS: Registering 1 stats: vmx.diskLibDataVmdkOpenTime
2025-06-12T13:54:52.656Z In(05) vmx VMXSTATS: Registering 2 stats: vmx.diskLibDataVmdkOpenTime
2025-06-12T13:54:52.656Z In(05) vmx VMXSTATS: Registering 3 stats: vmx.diskLibDataVmdkGrowTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 4 stats: vmx.diskLibDataVmdkGrowTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 5 stats: vmx.diskLibDigestVmdkOpenTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 6 stats: vmx.diskLibDigestVmdkOpenTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 7 stats: vmx.diskLibDigestVmdkGrowTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 8 stats: vmx.diskLibDigestVmdkGrowTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 9 stats: vmx.diskLibDigestFileDataGrowTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 10 stats: vmx.diskLibDigestFileDataGrowTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 11 stats: vmx.digestLibOpenIntTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 12 stats: vmx.digestLibOpenIntTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 13 stats: vmx.diskLibDataVmdkCloseTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 14 stats: vmx.diskLibDataVmdkCloseTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 15 stats: vmx.diskLibDigestVmdkCloseTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 16 stats: vmx.diskLibDigestVmdkCloseTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 17 stats: vmx.diskLibVmdkCreateTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 18 stats: vmx.diskLibVmdkCreateTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 19 stats: vmx.diskLibChildVmdkCreateTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 20 stats: vmx.diskLibChildVmdkCreateTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 21 stats: vmx.snapshotCreateTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 22 stats: vmx.snapshotCreateTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 23 stats: vmx.snapshotCreateQuiescedTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 24 stats: vmx.snapshotCreateQuiescedTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 25 stats: vmx.snapshotCreateMemoryTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 26 stats: vmx.snapshotCreateMemoryTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 27 stats: vmx.snapshotDeleteTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 28 stats: vmx.snapshotDeleteTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 29 stats: vmx.snapshotConsolidateTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 30 stats: vmx.snapshotConsolidateTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 31 stats: vmx.checkpointStunTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 32 stats: vmx.checkpointStunTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 33 stats: vmx.setPolicyTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 34 stats: vmx.setPolicyTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 35 stats: vmx.filtlibApplyDiskConfigTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 36 stats: vmx.filtlibApplyDiskConfigTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 37 stats: vmx.diskLibGetInfoTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 38 stats: vmx.diskLibGetInfoTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 39 stats: vmx.diskLibDigestGetInfoTime
2025-06-12T13:54:52.657Z In(05) vmx VMXSTATS: Registering 40 stats: vmx.diskLibDigestGetInfoTime
2025-06-12T13:54:52.657Z In(05) vmx Powering on guestOS 'windows7' using the configuration for 'windows7'.
2025-06-12T13:54:52.658Z In(05) vmx ToolsISO: open of /usr/lib/vmware/isoimages/isoimages_manifest.txt.sig failed: Could not find the file
2025-06-12T13:54:52.658Z In(05) vmx ToolsISO: Unable to read signature file '/usr/lib/vmware/isoimages/isoimages_manifest.txt.sig', ignoring.
2025-06-12T13:54:52.658Z In(05) vmx ToolsISO: Updated cached value for imageName to 'windows.iso'.
2025-06-12T13:54:52.658Z In(05) vmx ToolsISO: Selected Tools ISO 'windows.iso' for 'windows7' guest.
2025-06-12T13:54:52.658Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-06-12T13:54:52.658Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-06-12T13:54:52.659Z In(05) vmx Monitor Mode: CPL0
2025-06-12T13:54:52.666Z In(05) vmx OvhdMem_PowerOn: initial admission: paged   674445 nonpaged    36401 anonymous     6660
2025-06-12T13:54:52.666Z In(05) vmx VMMEM: Initial Reservation: 2802MB (MainMem=2048MB)
2025-06-12T13:54:52.666Z In(05) vmx llc: maximum vcpus per LLC: 1
2025-06-12T13:54:52.666Z In(05) vmx llc: vLLC size: 1
2025-06-12T13:54:52.666Z In(05) vmx MemSched_PowerOn: balloon minGuestSize 209715 (80% of min required size 262144)
2025-06-12T13:54:52.666Z In(05) vmx MemSched: reserved mem (in MB) min 128 max 41916 recommended 41916
2025-06-12T13:54:52.666Z In(05) vmx MemSched: pg 674445 np 36401 anon 6660 mem 524288
2025-06-12T13:54:52.666Z In(05) vmx MemSched: numvm 1 locked pages: num 0 max 10722304
2025-06-12T13:54:52.666Z In(05) vmx MemSched: locked Page Limit: host 11458922 config 10730496
2025-06-12T13:54:52.666Z In(05) vmx MemSched: minmempct 50 minalloc 0 admitted 1
2025-06-12T13:54:52.667Z In(05) vmx Host: Disabling thread priority boosting to work around Linux SMP bug.
2025-06-12T13:54:52.667Z In(05) vmx VMXSTATS: Registering 41 stats: vmx.logBytesDropped
2025-06-12T13:54:52.667Z In(05) vmx VMXSTATS: Registering 42 stats: vmx.logMsgsDropped
2025-06-12T13:54:52.667Z In(05) vmx VMXSTATS: Registering 43 stats: vmx.logBytesLogged
2025-06-12T13:54:52.667Z In(05) vmx VMXSTATS: Registering 44 stats: vmx.logWriteMinMaxTime
2025-06-12T13:54:52.667Z In(05) vmx VMXSTATS: Registering 45 stats: vmx.logWriteAvgTime
2025-06-12T13:54:52.667Z In(05) vmx LICENSE: Running unlicensed VMX (VMware Workstation)
2025-06-12T13:54:52.667Z In(05) vthread-42445 VTHREAD 123947928516288 "vthread-42445" tid 42445
2025-06-12T13:54:52.667Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-06-12T13:54:52.667Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-06-12T13:54:52.667Z In(05) vmx Host PA size: 39 bits. Guest PA size: 45 bits.
2025-06-12T13:54:52.668Z In(05) vmx ToolsISO: Refreshing imageName for 'windows7' (refreshCount=1, lastCount=1).
2025-06-12T13:54:52.668Z In(05) vmx ToolsISO: open of /usr/lib/vmware/isoimages/isoimages_manifest.txt.sig failed: Could not find the file
2025-06-12T13:54:52.668Z In(05) vmx ToolsISO: Unable to read signature file '/usr/lib/vmware/isoimages/isoimages_manifest.txt.sig', ignoring.
2025-06-12T13:54:52.668Z In(05) vmx ToolsISO: Updated cached value for imageName to 'windows.iso'.
2025-06-12T13:54:52.668Z In(05) vmx ToolsISO: Selected Tools ISO 'windows.iso' for 'windows7' guest.
2025-06-12T13:54:52.668Z In(05) deviceThread VTHREAD 123947920123584 "deviceThread" tid 42446
2025-06-12T13:54:52.669Z In(05) deviceThread Device thread is alive
2025-06-12T13:54:52.669Z In(05) vmx Host VT-x Capabilities:
2025-06-12T13:54:52.669Z In(05) vmx Basic VMX Information (0x03da050000000013)
2025-06-12T13:54:52.669Z In(05) vmx   VMCS revision ID                          19
2025-06-12T13:54:52.669Z In(05) vmx   VMCS region length                      1280
2025-06-12T13:54:52.669Z In(05) vmx   VMX physical-address width           natural
2025-06-12T13:54:52.669Z In(05) vmx   SMM dual-monitor mode                    yes
2025-06-12T13:54:52.669Z In(05) vmx   VMCS memory type                          WB
2025-06-12T13:54:52.669Z In(05) vmx   Advanced INS/OUTS info                   yes
2025-06-12T13:54:52.669Z In(05) vmx   True VMX MSRs                            yes
2025-06-12T13:54:52.669Z In(05) vmx   Exception Injection ignores error code   yes
2025-06-12T13:54:52.669Z In(05) vmx True Pin-Based VM-Execution Controls (0x000000ff00000016)
2025-06-12T13:54:52.669Z In(05) vmx   External-interrupt exiting               {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   NMI exiting                              {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   Virtual NMIs                             {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   Activate VMX-preemption timer            {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   Process posted interrupts                {0,1}
2025-06-12T13:54:52.669Z In(05) vmx True Primary Processor-Based VM-Execution Controls (0xfffbfffe04006172)
2025-06-12T13:54:52.669Z In(05) vmx   Interrupt-window exiting                 {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   Use TSC offsetting                       {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   HLT exiting                              {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   INVLPG exiting                           {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   MWAIT exiting                            {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   RDPMC exiting                            {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   RDTSC exiting                            {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   CR3-load exiting                         {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   CR3-store exiting                        {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   Activate tertiary controls               {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   CR8-load exiting                         {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   CR8-store exiting                        {0,1}
2025-06-12T13:54:52.669Z In(05) vmx   Use TPR shadow                           {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   NMI-window exiting                       {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   MOV-DR exiting                           {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Unconditional I/O exiting                {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Use I/O bitmaps                          {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Monitor trap flag                        {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Use MSR bitmaps                          {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   MONITOR exiting                          {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   PAUSE exiting                            {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Activate secondary controls              {0,1}
2025-06-12T13:54:52.670Z In(05) vmx Secondary Processor-Based VM-Execution Controls (0x075d7fff00000000)
2025-06-12T13:54:52.670Z In(05) vmx   Virtualize APIC accesses                 {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Enable EPT                               {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Descriptor-table exiting                 {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Enable RDTSCP                            {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Virtualize x2APIC mode                   {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Enable VPID                              {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   WBINVD exiting                           {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Unrestricted guest                       {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   APIC-register virtualization             {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Virtual-interrupt delivery               {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   PAUSE-loop exiting                       {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   RDRAND exiting                           {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Enable INVPCID                           {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Enable VM Functions                      {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Use VMCS shadowing                       {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   ENCLS exiting                            { 0 }
2025-06-12T13:54:52.670Z In(05) vmx   RDSEED exiting                           {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Enable PML                               { 0 }
2025-06-12T13:54:52.670Z In(05) vmx   EPT-violation #VE                        {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Conceal VMX from PT                      {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Enable XSAVES/XRSTORS                    {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   PASID translation                        { 0 }
2025-06-12T13:54:52.670Z In(05) vmx   Mode-based execute control for EPT       {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Sub-page write permissions for EPT       { 0 }
2025-06-12T13:54:52.670Z In(05) vmx   PT uses guest physical addresses         {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Use TSC scaling                          {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Enable UMWAIT and TPAUSE                 {0,1}
2025-06-12T13:54:52.670Z In(05) vmx   Enable ENCLV in VMX non-root mode        { 0 }
2025-06-12T13:54:52.670Z In(05) vmx   Enable EPC Virtualization Extensions     { 0 }
2025-06-12T13:54:52.670Z In(05) vmx   Bus lock exiting                         { 0 }
2025-06-12T13:54:52.670Z In(05) vmx   Notification VM exits                    { 0 }
2025-06-12T13:54:52.671Z In(05) vmx Tertiary Processor-Based VM-Execution Controls (0x0000000000000001)
2025-06-12T13:54:52.671Z In(05) vmx   LOADIWKEY exiting                         yes
2025-06-12T13:54:52.671Z In(05) vmx   Enable HLAT                                no
2025-06-12T13:54:52.671Z In(05) vmx   Enable Paging-Write                        no
2025-06-12T13:54:52.671Z In(05) vmx   Enable Guest Paging Verification           no
2025-06-12T13:54:52.671Z In(05) vmx   Enable IPI Virtualization                  no
2025-06-12T13:54:52.671Z In(05) vmx   Enable Virtual MSR_SPEC_CTRL               no
2025-06-12T13:54:52.671Z In(05) vmx True VM-Exit Controls (0xf77fffff00036dfb)
2025-06-12T13:54:52.671Z In(05) vmx   Save debug controls                      {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Host address-space size                  {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Acknowledge interrupt on exit            {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Save IA32_PAT                            {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Save IA32_EFER                           {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Save VMX-preemption timer                {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Clear IA32_BNDCFGS                       { 0 }
2025-06-12T13:54:52.671Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Clear IA32_RTIT MSR                      {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Clear IA32_LBR_CTL MSR                   {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Clear user-interrupt notification vector { 0 }
2025-06-12T13:54:52.671Z In(05) vmx   Load CET state                           {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load PKRS                                {0,1}
2025-06-12T13:54:52.671Z In(05) vmx True VM-Entry Controls (0x0076ffff000011fb)
2025-06-12T13:54:52.671Z In(05) vmx   Load debug controls                      {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   IA-32e mode guest                        {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Entry to SMM                             {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Deactivate dual-monitor mode             {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load IA32_BNDCFGS                        { 0 }
2025-06-12T13:54:52.671Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load IA32_RTIT MSR                       {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load user-interrupt notification vector  { 0 }
2025-06-12T13:54:52.671Z In(05) vmx   Load CET state                           {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load IA32_LBR_CTL MSR                    {0,1}
2025-06-12T13:54:52.671Z In(05) vmx   Load PKRS                                {0,1}
2025-06-12T13:54:52.671Z In(05) vmx VPID and EPT Capabilities (0x00000f0106f34141)
2025-06-12T13:54:52.671Z In(05) vmx   R=0/W=0/X=1                               yes
2025-06-12T13:54:52.671Z In(05) vmx   Page-walk length 3                        yes
2025-06-12T13:54:52.672Z In(05) vmx   EPT memory type WB                        yes
2025-06-12T13:54:52.672Z In(05) vmx   2MB super-page                            yes
2025-06-12T13:54:52.672Z In(05) vmx   1GB super-page                            yes
2025-06-12T13:54:52.672Z In(05) vmx   INVEPT support                            yes
2025-06-12T13:54:52.672Z In(05) vmx   Access & Dirty Bits                       yes
2025-06-12T13:54:52.672Z In(05) vmx   Advanced VM exit information for EPT violations   yes
2025-06-12T13:54:52.672Z In(05) vmx   Supervisor shadow-stack control           yes
2025-06-12T13:54:52.672Z In(05) vmx   Type 1 INVEPT                             yes
2025-06-12T13:54:52.672Z In(05) vmx   Type 2 INVEPT                             yes
2025-06-12T13:54:52.672Z In(05) vmx   INVVPID support                           yes
2025-06-12T13:54:52.672Z In(05) vmx   Type 0 INVVPID                            yes
2025-06-12T13:54:52.672Z In(05) vmx   Type 1 INVVPID                            yes
2025-06-12T13:54:52.672Z In(05) vmx   Type 2 INVVPID                            yes
2025-06-12T13:54:52.672Z In(05) vmx   Type 3 INVVPID                            yes
2025-06-12T13:54:52.672Z In(05) vmx Miscellaneous VMX Data (0x000000007004c1e7)
2025-06-12T13:54:52.672Z In(05) vmx   TSC to preemption timer ratio      7
2025-06-12T13:54:52.672Z In(05) vmx   VM-Exit saves EFER.LMA           yes
2025-06-12T13:54:52.672Z In(05) vmx   Activity State HLT               yes
2025-06-12T13:54:52.672Z In(05) vmx   Activity State shutdown          yes
2025-06-12T13:54:52.672Z In(05) vmx   Activity State wait-for-SIPI     yes
2025-06-12T13:54:52.672Z In(05) vmx   Processor trace in VMX           yes
2025-06-12T13:54:52.672Z In(05) vmx   RDMSR SMBASE MSR in SMM          yes
2025-06-12T13:54:52.672Z In(05) vmx   CR3 targets supported              4
2025-06-12T13:54:52.672Z In(05) vmx   Maximum MSR list size            512
2025-06-12T13:54:52.672Z In(05) vmx   VMXOFF holdoff of SMIs           yes
2025-06-12T13:54:52.672Z In(05) vmx   Allow all VMWRITEs               yes
2025-06-12T13:54:52.672Z In(05) vmx   Allow zero instruction length    yes
2025-06-12T13:54:52.672Z In(05) vmx   MSEG revision ID                   0
2025-06-12T13:54:52.672Z In(05) vmx VMX-Fixed Bits in CR0 (0x0000000080000021/0x00000000ffffffff)
2025-06-12T13:54:52.672Z In(05) vmx   Fixed to 0        0xffffffff00000000
2025-06-12T13:54:52.672Z In(05) vmx   Fixed to 1        0x0000000080000021
2025-06-12T13:54:52.672Z In(05) vmx   Variable          0x000000007fffffde
2025-06-12T13:54:52.672Z In(05) vmx VMX-Fixed Bits in CR4 (0x0000000000002000/0x0000000001ff2fff)
2025-06-12T13:54:52.672Z In(05) vmx   Fixed to 0        0xfffffffffe00d000
2025-06-12T13:54:52.672Z In(05) vmx   Fixed to 1        0x0000000000002000
2025-06-12T13:54:52.672Z In(05) vmx   Variable          0x0000000001ff0fff
2025-06-12T13:54:52.672Z In(05) vmx VMCS Enumeration (0x000000000000002e)
2025-06-12T13:54:52.672Z In(05) vmx   Highest index                   0x17
2025-06-12T13:54:52.672Z In(05) vmx VM Functions (0x0000000000000001)
2025-06-12T13:54:52.672Z In(05) vmx   Function  0 (EPTP-switching) supported.
2025-06-12T13:54:52.672Z In(05) vmx Monitor_PowerOn: hostedVSMPMaxSkew is 1500 us (3456000 cycles)
2025-06-12T13:54:52.673Z In(05) vmx vmm-modules: [vmm.vmm, vmce-vmce.vmm, viommu-none.vmm, vprobe-none.vmm, hv-vt.vmm, gphys-ept.vmm, callstack-none.vmm, !e1000Shared=0x0, !tdxSharedVMData=0x880, !vmSamples=0x880, !theIOSpace=0x8c0, !ttGPPerVcpu=0x76c0, {UseUnwind}=0x0, numVCPUsAsAddr=0x2, {SharedAreaReservations}=0x7700, {rodataSize}=0x20a60, {textAddr}=0xfffffffffc000000, {textSize}=0x8eab9, <MonSrcFile>]
2025-06-12T13:54:52.673Z In(05) vmx vmm-vcpus:   2
2025-06-12T13:54:52.679Z In(05) vmx KHZEstimate 2304000
2025-06-12T13:54:52.679Z In(05) vmx MHZEstimate 2304
2025-06-12T13:54:52.679Z In(05) vmx NumVCPUs 2
2025-06-12T13:54:52.679Z In(05) vmx UUIDHostSearch: UUID is reported as '80 04 92 55 3b 62 ef 11-91 8f e1 bd dd 1a 0e 00'.
2025-06-12T13:54:52.679Z In(05) vmx AIOGNRC: numThreads=18 ide=0, scsi=1, passthru=1
2025-06-12T13:54:52.679Z In(05) vmx WORKER: Creating new group with maxThreads=18 (18)
2025-06-12T13:54:52.683Z In(05) vmx WORKER: Creating new group with maxThreads=1 (19)
2025-06-12T13:54:52.683Z In(05) vmx MainMem: CPT Host WZ=0 PF=2048 D=0
2025-06-12T13:54:52.683Z In(05) vmx MainMem: CPT PLS=1 PLR=1 BS=1 BlkP=32 Mult=4 W=50
2025-06-12T13:54:52.683Z In(05) vmem VTHREAD 123947907536576 "vmem" tid 42447
2025-06-12T13:54:53.065Z In(05) vmx MainMem: Opened paging file, '/tmp/vmware-geyee/564dce07-2c9c-7a53-4e18-a8c31ed7f000/vmem' (swap).
2025-06-12T13:54:53.065Z No(00) vmx PowerOnTiming: Module MainMem took 385778 us
2025-06-12T13:54:53.065Z In(05) vmx MStat: Creating Stat vm.uptime
2025-06-12T13:54:53.065Z In(05) vmx MStat: Creating Stat vm.suspendTime
2025-06-12T13:54:53.065Z In(05) vmx MStat: Creating Stat vm.powerOnTimeStamp
2025-06-12T13:54:53.066Z In(05) vmx VMXAIOMGR: Using: simple=Generic
2025-06-12T13:54:53.074Z In(05) vmx WORKER: Creating new group with maxThreads=1 (20)
2025-06-12T13:54:53.075Z In(05) vmx WORKER: Creating new group with maxThreads=1 (21)
2025-06-12T13:54:53.075Z In(05) vmx WORKER: Creating new group with maxThreads=14 (35)
2025-06-12T13:54:53.075Z In(05) vmx FeatureCompat: No VM masks.
2025-06-12T13:54:53.075Z In(05) vmx TimeTracker host to guest rate conversion 1079458744 @ 2304000000Hz -> 0 @ 2304000000Hz
2025-06-12T13:54:53.075Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + -1079458744
2025-06-12T13:54:53.075Z In(05) vmx TSC scaling enabled.
2025-06-12T13:54:53.075Z In(05) vmx TSC offsetting enabled.
2025-06-12T13:54:53.075Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2025-06-12T13:54:53.075Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2025-06-12T13:54:53.076Z In(05) vmx MKS PowerOn
2025-06-12T13:54:53.076Z In(05) mks VTHREAD 123947907536576 "mks" tid 42449
2025-06-12T13:54:53.076Z In(05) mks MKS thread is alive
2025-06-12T13:54:53.076Z In(05) svga VTHREAD 123947899143872 "svga" tid 42450
2025-06-12T13:54:53.076Z In(05) svga SVGA thread is alive
2025-06-12T13:54:53.077Z In(05) mks MKS: SSE2=1, SSSE3=1, SSE4_1=1
2025-06-12T13:54:53.078Z In(05) mks MKS-RenderMain: PowerOn allowed MKSBasicOps 
2025-06-12T13:54:53.078Z In(05) mks MKS-RenderMain: ISB enabled by config
2025-06-12T13:54:53.078Z In(05) mks MKS-RenderMain: Collecting RenderOps caps from MKSBasicOps
2025-06-12T13:54:53.078Z In(05) mks MKS-RenderMain: Starting MKSBasicOps
2025-06-12T13:54:53.078Z In(05) mks MKS-RenderMain: Started MKSBasicOps
2025-06-12T13:54:53.078Z In(05) mks MKS-RenderMain: Found Full Renderer: MKSBasicOps
2025-06-12T13:54:53.078Z In(05) mks MKS-RenderMain: maxTextureSize=32768
2025-06-12T13:54:53.078Z In(05) mks SOCKET 1 (80) creating new listening socket on port -1
2025-06-12T13:54:53.078Z In(05) mks KHBKL: Unable to parse keystring at: ''
2025-06-12T13:54:53.078Z In(05) mks MKSRemoteMgr: Set default display name: Windows 7 Clone
2025-06-12T13:54:53.078Z In(05) mks MKSRemoteMgr: Loading VNC Configuration from VM config file
2025-06-12T13:54:53.078Z In(05) mks MKSRemoteMgr: Using default VNC keymap table "us"
2025-06-12T13:54:53.078Z In(05) vmx VLANCE: send cluster threshold is 80, size = 2 recalcInterval is 20000 us
2025-06-12T13:54:53.078Z In(05) vmx VMXNET: send cluster threshold is 80, size = 2 recalcInterval is 20000 ticks, dontClusterSize is 128
2025-06-12T13:54:53.079Z In(05) vmx Chipset version: 0x13
2025-06-12T13:54:53.082Z In(05) vmx SOUNDLIB: SoundLib_CreatePulseBackend: waiting for connection to Pulse server
2025-06-12T13:54:53.095Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "-1"
2025-06-12T13:54:53.095Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "21"
2025-06-12T13:54:53.097Z In(05) vmx VMXSTATS: Registering 46 stats: vmx.configWriteMinMaxTime
2025-06-12T13:54:53.097Z In(05) vmx VMXSTATS: Registering 47 stats: vmx.configWriteAvgTime
2025-06-12T13:54:53.101Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation begins.
2025-06-12T13:54:53.101Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation completes.
2025-06-12T13:54:53.101Z No(00) vmx ConfigDB: Setting scsi0:0.redo = ""
2025-06-12T13:54:53.101Z In(05) vmx DISK: OPEN scsi0:0 '/mnt/vm/vm_lonele/Windows 7-cl1-000002.vmdk' persistent R[]
2025-06-12T13:54:53.103Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s012.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.103Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s012.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.103Z In(05) vmx DISKLIB-SPARSE: "/mnt/vm/vm_lonele/Windows 7-cl1-000002-s012.vmdk" : failed to open (14): Disk needs repair.
2025-06-12T13:54:53.104Z Er(02) vmx DISKLIB-LINK  : DiskLinkOpen: Failed to open '/mnt/vm/vm_lonele/Windows 7-cl1-000002.vmdk': : The specified virtual disk needs repair
2025-06-12T13:54:53.104Z Er(02) vmx DISKLIB-CHAIN : DiskChainOpen: "/mnt/vm/vm_lonele/Windows 7-cl1-000002.vmdk": failed to open: The specified virtual disk needs repair.
2025-06-12T13:54:53.104Z In(05) vmx DISKLIB-LIB   : Failed to open '/mnt/vm/vm_lonele/Windows 7-cl1-000002.vmdk' with flags 0xa The specified virtual disk needs repair (14).
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s012.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s012.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s013.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s013.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s014.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s014.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s015.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s015.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s016.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s016.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s017.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s017.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s018.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.118Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s018.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s019.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s019.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s020.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s020.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s022.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s022.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s023.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s023.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s024.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s024.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s025.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s025.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s026.vmdk] 512 excess bytes at end of file
2025-06-12T13:54:53.119Z In(05) vmx DISKLIB-SPARSECHK: [/mnt/vm/vm_lonele/Windows 7-cl1-000002-s026.vmdk] The file appears to have been copied in text mode, with newline conversion.
2025-06-12T13:54:53.119Z In(05) vmx This is bug 1683.
2025-06-12T13:54:53.119Z In(05) vmx DISK: Opening disks took 18 ms.
2025-06-12T13:54:53.119Z In(05) vmx Module 'Disk' power on failed.
2025-06-12T13:54:53.119Z No(00) vmx PowerOnTiming: ALL took 471261 us
2025-06-12T13:54:53.119Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 0
2025-06-12T13:54:53.120Z In(05) vmx MKSThread: Requesting MKS exit
2025-06-12T13:54:53.120Z In(05) vmx Stopping MKS/SVGA threads
2025-06-12T13:54:53.120Z In(05) svga MKSThread: SVGA thread is skipping PowerOn
2025-06-12T13:54:53.120Z In(05) svga MKSThread: SVGA thread is skipping the main loop
2025-06-12T13:54:53.120Z In(05) vmx MKS/SVGA threads are stopped
2025-06-12T13:54:53.120Z In(05) mks MKS-RenderMain: Stopping MKSBasicOps
2025-06-12T13:54:53.120Z In(05) mks MKS-RenderMain: Stopping MKSBasicOps
2025-06-12T13:54:53.120Z In(05) mks MKS-RenderMain: Stopped MKSBasicOps
2025-06-12T13:54:53.120Z In(05) mks MKS PowerOff
2025-06-12T13:54:53.120Z In(05) svga MKSThread: SVGA thread is skipping PowerOff
2025-06-12T13:54:53.120Z In(05) svga SVGA thread is exiting
2025-06-12T13:54:53.120Z In(05) mks MKS thread is exiting
2025-06-12T13:54:53.121Z Wa(03) vmx 
2025-06-12T13:54:53.241Z In(05) deviceThread Device thread is exiting
2025-06-12T13:54:53.241Z In(05) vmx Vix: [mainDispatch.c:1171]: VMAutomationPowerOff: Powering off.
2025-06-12T13:54:53.241Z Wa(03) vmx /mnt/vm/vm_lonele/Windows7_Clone_Simple.vmx: Cannot remove symlink /var/run/vmware/1000/**********_42376/configFile: No such file or directory
2025-06-12T13:54:53.241Z In(05) vmx Policy_SavePolicyFile: invalid arguments to function.
2025-06-12T13:54:53.241Z In(05) vmx PolicyVMX_Exit: Could not write out policies: 15.
2025-06-12T13:54:53.241Z In(05) vmx WORKER: asyncOps=1 maxActiveOps=1 maxPending=1 maxCompleted=0
2025-06-12T13:54:53.243Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-06-12T13:54:53.243Z In(05) vmx VMXSTATS: Ready to cleanup and munmap 70BAE2393000.
2025-06-12T13:54:53.243Z In(05) vmx 
2025-06-12T13:54:53.243Z In(05)+ vmx Power on failure messages: The disk '/mnt/vm/vm_lonele/Windows 7-cl1-000002.vmdk' has one or more internal errors that cannot be fixed. Restore from a backup copy of this disk.
2025-06-12T13:54:53.243Z In(05)+ vmx The specified virtual disk needs repair
2025-06-12T13:54:53.243Z In(05)+ vmx Cannot open the disk '/mnt/vm/vm_lonele/Windows 7-cl1-000002.vmdk' or one of the snapshot disks it depends on.
2025-06-12T13:54:53.243Z In(05)+ vmx Module 'Disk' power on failed.
2025-06-12T13:54:53.243Z In(05)+ vmx Failed to start the virtual machine.
2025-06-12T13:54:53.243Z In(05)+ vmx 
2025-06-12T13:54:53.244Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-06-12T13:54:53.244Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-06-12T13:54:53.244Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=0 additionalError=0
2025-06-12T13:54:53.244Z In(05) vmx Vix: [mainDispatch.c:4250]: Error VIX_E_FAIL in VMAutomation_ReportPowerOpFinished(): Unknown error
2025-06-12T13:54:53.244Z In(05) vmx Vix: [mainDispatch.c:4210]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-06-12T13:54:53.244Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-06-12T13:54:53.244Z In(05) vmx WQPoolFreePoll : pollIx = 3, signalHandle = 17
2025-06-12T13:54:53.244Z In(05) vmx Vix: [mainDispatch.c:817]: VMAutomation_LateShutdown()
2025-06-12T13:54:53.244Z In(05) vmx Vix: [mainDispatch.c:772]: VMAutomationCloseListenerSocket. Closing listener socket.
2025-06-12T13:54:53.245Z In(05) vmx Flushing VMX VMDB connections
2025-06-12T13:54:53.245Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2025-06-12T13:54:53.245Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (0)
2025-06-12T13:54:53.245Z In(05) vmx VigorTransport_ServerDestroy: server destroyed.
2025-06-12T13:54:53.245Z In(05) vmx WQPoolFreePoll : pollIx = 2, signalHandle = 8
2025-06-12T13:54:53.245Z In(05) vmx WQPoolFreePoll : pollIx = 1, signalHandle = 9
2025-06-12T13:54:53.250Z In(05) vmx VMX exit (0).
2025-06-12T13:54:53.250Z In(05) vmx OBJLIB-LIB: ObjLib cleanup done.
2025-06-12T13:54:53.250Z In(05) vmx AIOMGR-S : stat o=2 r=6 w=0 i=0 br=98304 bw=0
