#!/usr/bin/env python3
"""
VMDK文件选择器
快速分析并推荐应该使用哪个VMDK文件作为虚拟机硬盘
"""

import struct
import os
import glob
from datetime import datetime

def is_vmdk_descriptor(filename):
    """检查是否为VMDK描述符文件（文本文件）"""
    try:
        with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
            first_line = f.readline().strip()
            return first_line.startswith('# Disk DescriptorFile')
    except:
        return False

def is_vmdk_data_file(filename):
    """检查是否为VMDK数据文件（二进制文件）"""
    try:
        with open(filename, 'rb') as f:
            magic = f.read(4)
            return magic == b'KDMV'
    except:
        return False

def parse_vmdk_descriptor(filename):
    """解析VMDK描述符文件"""
    info = {
        'filename': filename,
        'parent': None,
        'cid': None,
        'parent_cid': None,
        'extents': []
    }

    try:
        with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
            for line in f:
                line = line.strip()
                if line.startswith('CID='):
                    info['cid'] = line.split('=')[1]
                elif line.startswith('parentCID='):
                    info['parent_cid'] = line.split('=')[1]
                elif line.startswith('parentFileNameHint='):
                    info['parent'] = line.split('=')[1].strip('"')
                elif line.startswith('RW ') and 'SPARSE' in line:
                    # 解析extent行: RW 8323072 SPARSE "filename.vmdk"
                    # 使用更精确的方法提取文件名
                    quote_start = line.find('"')
                    quote_end = line.rfind('"')
                    if quote_start != -1 and quote_end != -1 and quote_start < quote_end:
                        extent_file = line[quote_start+1:quote_end]
                        info['extents'].append(extent_file)
    except:
        pass

    return info

def get_file_info(filename):
    """获取文件基本信息"""
    try:
        stat = os.stat(filename)
        return {
            'size': stat.st_size,
            'mtime': datetime.fromtimestamp(stat.st_mtime),
            'exists': True
        }
    except:
        return {'exists': False}

def analyze_vmdk_chain():
    """分析当前目录中的VMDK文件链"""
    print("VMDK文件链分析器")
    print("=" * 50)

    # 查找所有VMDK文件
    vmdk_files = glob.glob("*.vmdk")
    if not vmdk_files:
        print("当前目录中没有找到VMDK文件")
        return

    # 分类文件
    descriptors = []
    data_files = []

    for filename in vmdk_files:
        if is_vmdk_descriptor(filename):
            descriptors.append(filename)
        elif is_vmdk_data_file(filename):
            data_files.append(filename)

    print(f"找到 {len(descriptors)} 个描述符文件")
    print(f"找到 {len(data_files)} 个数据文件")
    print()

    # 分析描述符文件
    descriptor_info = {}
    for desc in descriptors:
        info = parse_vmdk_descriptor(desc)
        file_info = get_file_info(desc)
        info.update(file_info)
        descriptor_info[desc] = info

    # 构建快照链
    print("快照链分析:")
    print("-" * 30)

    # 找到基础磁盘（没有父磁盘的）
    base_disks = []
    snapshot_disks = []

    for desc, info in descriptor_info.items():
        if info['parent_cid'] == 'ffffffff' or not info['parent']:
            base_disks.append((desc, info))
        else:
            snapshot_disks.append((desc, info))

    # 按时间排序快照
    snapshot_disks.sort(key=lambda x: x[1]['mtime'] if x[1]['exists'] else datetime.min)

    # 显示快照链
    for base_desc, base_info in base_disks:
        print(f"📁 基础磁盘: {base_desc}")
        print(f"   创建时间: {base_info['mtime'].strftime('%Y-%m-%d %H:%M:%S') if base_info['exists'] else 'N/A'}")
        print(f"   CID: {base_info['cid']}")

        # 找到这个基础磁盘的快照链
        current_cid = base_info['cid']
        level = 1

        while True:
            found_child = False
            for snap_desc, snap_info in snapshot_disks:
                if snap_info['parent_cid'] == current_cid:
                    indent = "   " + "└─ " * level
                    print(f"{indent}📸 快照: {snap_desc}")
                    print(f"{indent}   时间: {snap_info['mtime'].strftime('%Y-%m-%d %H:%M:%S') if snap_info['exists'] else 'N/A'}")
                    print(f"{indent}   CID: {snap_info['cid']}")

                    current_cid = snap_info['cid']
                    level += 1
                    found_child = True
                    break

            if not found_child:
                break

    print()

    # 推荐选择
    print("推荐选择:")
    print("-" * 30)

    if snapshot_disks:
        # 选择最新的快照
        latest_snapshot = max(snapshot_disks, key=lambda x: x[1]['mtime'] if x[1]['exists'] else datetime.min)
        recommended = latest_snapshot[0]

        print(f"🎯 推荐使用: {recommended}")
        print(f"   原因: 这是最新的快照，包含最完整的虚拟机状态")
        print(f"   时间: {latest_snapshot[1]['mtime'].strftime('%Y-%m-%d %H:%M:%S')}")

        # 检查依赖的数据文件是否存在
        missing_files = []
        existing_files = []
        for extent in latest_snapshot[1]['extents']:
            if not os.path.exists(extent):
                missing_files.append(extent)
            else:
                existing_files.append(extent)

        print(f"   📊 数据文件状态:")
        print(f"      存在: {len(existing_files)} 个")
        print(f"      缺失: {len(missing_files)} 个")

        if len(existing_files) > 0:
            print(f"   ✅ 找到 {len(existing_files)} 个数据文件，虚拟机应该可以启动")
            if len(missing_files) > 0:
                print(f"   💡 缺失的文件是正常的（稀疏格式特性）")
        else:
            print(f"   ❌ 警告: 没有找到任何数据文件，虚拟机可能无法启动")

    elif base_disks:
        # 只有基础磁盘
        latest_base = max(base_disks, key=lambda x: x[1]['mtime'] if x[1]['exists'] else datetime.min)
        recommended = latest_base[0]

        print(f"🎯 推荐使用: {recommended}")
        print(f"   原因: 这是基础磁盘，没有快照")
        print(f"   时间: {latest_base[1]['mtime'].strftime('%Y-%m-%d %H:%M:%S')}")

    else:
        print("❌ 没有找到有效的VMDK描述符文件")
        return

    print()

    # 生成VMX配置建议
    print("VMX配置建议:")
    print("-" * 30)
    print(f'scsi0:0.fileName = "{recommended}"')
    print('scsi0:0.present = "TRUE"')
    print('scsi0:0.deviceType = "scsi-hardDisk"')

    print()
    print("使用说明:")
    print("1. 将上述配置添加到你的VMX文件中")
    print("2. 确保所有相关的数据文件(.vmdk)都在同一目录")
    print("3. 如果使用快照，请保持完整的快照链")

def main():
    """主函数"""
    try:
        analyze_vmdk_chain()
    except KeyboardInterrupt:
        print("\n操作被用户取消")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
