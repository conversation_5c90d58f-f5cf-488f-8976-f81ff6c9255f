#!/usr/bin/vmware
.encoding = "UTF-8"
config.version = "8"
virtualHW.version = "19"

displayName = "Windows 7 Clone"
guestOS = "windows7"

numvcpus = "2"
memsize = "2048"

scsi0.present = "TRUE"
scsi0.virtualDev = "lsilogic"
scsi0:0.present = "TRUE"
scsi0:0.deviceType = "disk"
scsi0:0.fileName = "Windows 7-cl1-000002.vmdk"

ide1:0.present = "TRUE"
ide1:0.deviceType = "cdrom-image"
ide1:0.startConnected = "FALSE"

ethernet0.present = "TRUE"
ethernet0.virtualDev = "e1000"
ethernet0.networkName = "NAT"
ethernet0.addressType = "generated"

sound.present = "TRUE"
sound.virtualDev = "hdaudio"

usb.present = "TRUE"
ehci.present = "TRUE"

pciBridge0.present = "TRUE"
pciBridge4.present = "TRUE"
pciBridge4.virtualDev = "pcieRootPort"
pciBridge4.functions = "8"

vmci0.present = "TRUE"
hpet0.present = "TRUE"

powerType.powerOff = "soft"
powerType.powerOn = "soft"
powerType.suspend = "soft"
powerType.reset = "soft"

cleanShutdown = "TRUE"
checkpoint.vmState = "Windows 7 的克隆-Snapshot2.vmsn"
extendedConfigFile = "Windows7_Clone_Simple.vmxf"
virtualHW.productCompatibility = "hosted"
fileSearchPath = "/mnt/vm/vm_lonele;."
vmxstats.filename = "Windows7_Clone_Simple.scoreboard"
