# VMDK分析工具使用说明

这些Python脚本用于分析VMware虚拟磁盘文件(VMDK)的结构和内容，帮助确定应该选择哪个VMDK文件作为虚拟机的硬盘。

## 工具列表

### 1. vmdk_analyzer.py - 详细分析工具
深入分析VMDK文件的二进制结构，包括：
- VMDK头部信息解析
- 粒度表使用情况分析
- 文件系统签名检测
- 数据分配率统计

### 2. vmdk_selector.py - 快速选择工具
快速分析VMDK文件链并推荐最佳选择：
- 自动识别快照链结构
- 推荐最新的可用快照
- 生成VMX配置建议
- 检查文件完整性

## 使用方法

### 快速选择（推荐）
```bash
# 在包含VMDK文件的目录中运行
python3 vmdk_selector.py
```

### 详细分析
```bash
# 分析单个文件
python3 vmdk_analyzer.py "Windows 7-cl1-000002.vmdk"

# 分析多个文件
python3 vmdk_analyzer.py "Windows 7-cl1.vmdk" "Windows 7-cl1-000001.vmdk" "Windows 7-cl1-000002.vmdk"
```

## 输出解释

### vmdk_selector.py 输出
- **📁 基础磁盘**: 原始的虚拟磁盘文件
- **📸 快照**: 基于基础磁盘创建的快照
- **🎯 推荐使用**: 建议选择的VMDK文件
- **✅/⚠️**: 文件完整性状态

### vmdk_analyzer.py 输出
- **魔数**: 应该是"KDMV"（VMware磁盘格式标识）
- **容量**: 虚拟磁盘的总容量
- **数据分配率**: 实际使用的存储空间百分比
- **稀疏度**: 节省的存储空间百分比
- **文件系统签名**: 检测到的文件系统类型

## 技术原理

### VMDK文件类型
1. **描述符文件** (.vmdk文本文件)
   - 包含磁盘配置信息
   - 列出所有数据文件
   - 定义快照关系

2. **数据文件** (.vmdk二进制文件，通常带-s###后缀)
   - 存储实际的磁盘数据
   - 使用稀疏格式节省空间
   - 支持压缩和增量存储

### 快照链结构
```
基础磁盘 (Windows 7-cl1.vmdk)
    ↓
快照1 (Windows 7-cl1-000001.vmdk)
    ↓
快照2 (Windows 7-cl1-000002.vmdk) ← 当前状态
```

### 选择原则
1. **优先选择最新快照**: 包含最完整的系统状态
2. **检查文件完整性**: 确保所有依赖文件存在
3. **验证文件系统**: 确认包含有效的引导记录和文件系统

## 常见问题

### Q: 为什么快照文件比基础磁盘小？
A: 快照使用写时复制(COW)技术，只存储变更的数据块，大大节省存储空间。

### Q: 数据分配率低是否意味着数据不完整？
A: 不是。低分配率是快照的正常特征，未修改的数据仍从父快照读取。

### Q: 如何处理缺失的数据文件？
A: 如果缺少关键的数据文件，虚拟机可能无法启动。需要：
1. 恢复缺失的文件
2. 或选择较早但完整的快照
3. 或从备份中恢复

### Q: 可以删除旧的快照吗？
A: 不建议直接删除。应该使用VMware工具合并快照，否则可能破坏快照链。

## 示例输出

```
VMDK文件链分析器
==================================================
找到 3 个描述符文件
找到 32 个数据文件

快照链分析:
------------------------------
📁 基础磁盘: Windows 7-cl1.vmdk
   创建时间: 2023-12-29 10:30:15
   CID: afbb51dd
   └─ 📸 快照: Windows 7-cl1-000001.vmdk
      时间: 2024-01-08 14:20:30
      CID: cce71c3d
      └─ 📸 快照: Windows 7-cl1-000002.vmdk
         时间: 2024-01-20 16:45:22
         CID: 51e8494e

推荐选择:
------------------------------
🎯 推荐使用: Windows 7-cl1-000002.vmdk
   原因: 这是最新的快照，包含最完整的虚拟机状态
   时间: 2024-01-20 16:45:22
   ✅ 所有依赖的数据文件都存在

VMX配置建议:
------------------------------
scsi0:0.fileName = "Windows 7-cl1-000002.vmdk"
scsi0:0.present = "TRUE"
scsi0:0.deviceType = "scsi-hardDisk"
```

## 注意事项

1. **备份重要数据**: 在进行任何操作前，请备份整个虚拟机目录
2. **保持文件完整性**: 不要移动或删除任何VMDK相关文件
3. **使用正确的VMware版本**: 确保VMware版本支持这些VMDK文件
4. **检查磁盘空间**: 确保有足够的磁盘空间来运行虚拟机

## 故障排除

如果脚本运行出错：
1. 检查Python版本（需要Python 3.6+）
2. 确保在正确的目录中运行
3. 检查文件权限
4. 查看错误信息并相应处理

## 更新日志

- v1.0: 初始版本，支持基本的VMDK分析和选择功能
- 包含详细的二进制结构分析
- 支持快照链自动识别和推荐
