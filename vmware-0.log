2024-01-20T08:10:04.163Z In(05) vmx Log for VMware Workstation pid=8912 version=16.2.1 build=build-18811642 option=Release
2024-01-20T08:10:04.163Z In(05) vmx The host is 64-bit.
2024-01-20T08:10:04.163Z In(05) vmx Host codepage=windows-936 encoding=GBK
2024-01-20T08:10:04.163Z In(05) vmx Host is Windows 10 Pro, 64-bit (Build 22000.1042)
2024-01-20T08:10:04.163Z In(05) vmx Host offset from UTC is -08:00.
2024-01-20T08:10:04.069Z In(05) vmx VTHREAD 8776 "vmx"
2024-01-20T08:10:04.072Z In(05) vmx LOCALE GBK -> zh_CN User=804 System=804
2024-01-20T08:10:04.072Z In(05) vmx Msg_SetLocaleEx: HostLocale=GBK UserLocale=zh_CN
2024-01-20T08:10:04.141Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-01-20T08:10:04.141Z In(05) vmx Msg_Reset:
2024-01-20T08:10:04.141Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-01-20T08:10:04.141Z In(05) vmx ----------------------------------------
2024-01-20T08:10:04.141Z In(05) vmx ConfigDB: Failed to load C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2024-01-20T08:10:04.142Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("F:\vm_lonele\Windows 7 的克隆.vmpl", ...) failed, error: 2
2024-01-20T08:10:04.142Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2024-01-20T08:10:04.145Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-01-20T08:10:04.145Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-01-20T08:10:04.145Z In(05) vmx PREF Optional preferences file not found at C:\Users\<USER>\AppData\Roaming\VMware\config.ini. Using default values.
2024-01-20T08:10:04.146Z In(05) vmx UUID: SMBIOS UUID is reported as '40 1e 70 61 c7 fe d5 11-80 68 48 5b 39 ca a5 a7'.
2024-01-20T08:10:04.155Z In(05) vmx lib/ssl: OpenSSL using RAND_OpenSSL for RAND
2024-01-20T08:10:04.156Z In(05) vmx lib/ssl: protocol list tls1.2
2024-01-20T08:10:04.156Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2024-01-20T08:10:04.156Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2024-01-20T08:10:04.156Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2024-01-20T08:10:04.168Z In(05) vmx Hostname=asdf
2024-01-20T08:10:04.179Z In(05) vmx IP=fe80::99d0:ec2d:b2e7:536b%9
2024-01-20T08:10:04.179Z In(05) vmx IP=fe80::5c24:4d2c:cb3b:f35f%21
2024-01-20T08:10:04.179Z In(05) vmx IP=fe80::b0bd:fe61:52ce:a4b7%10
2024-01-20T08:10:04.179Z In(05) vmx IP=240e:604:10c:247::112
2024-01-20T08:10:04.179Z In(05) vmx IP=*************
2024-01-20T08:10:04.179Z In(05) vmx IP=***********
2024-01-20T08:10:04.179Z In(05) vmx IP=************
2024-01-20T08:10:04.179Z In(05) vmx IP=fd7a:115c:a1e0:ab12:4843:cd96:6266:5639
2024-01-20T08:10:04.179Z In(05) vmx IP=fc04:7caa:8acd:dfa8:e781::1
2024-01-20T08:10:04.202Z In(05) vmx System uptime 408117005 us
2024-01-20T08:10:04.202Z In(05) vmx Command line: "C:\Program Files (x86)\VMware\VMware Workstation\x64\vmware-vmx.exe" "-T" "querytoken" "-E" "zh_CN" "-s" "vmx.stdio.keep=TRUE" "-#" "product=1;name=VMware Workstation;version=16.2.1;buildnumber=18811642;licensename=VMware Workstation;licenseversion=16.0;" "-@" "pipe=\\.\pipe\vmx0267290113d702d0;msgs=ui" "F:\vm_lonele\Windows 7 的克隆.vmx"
2024-01-20T08:10:04.202Z In(05) vmx Msg_SetLocaleEx: HostLocale=GBK UserLocale=zh_CN
2024-01-20T08:10:04.309Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 724
2024-01-20T08:10:04.309Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 676
2024-01-20T08:10:04.311Z In(05) vmx VigorTransport listening on fd 844
2024-01-20T08:10:04.311Z In(05) vmx Vigor_Init 1
2024-01-20T08:10:04.311Z In(05) vmx Connecting 'ui' to pipe '\\.\pipe\vmx0267290113d702d0' with user '(null)'
2024-01-20T08:10:04.311Z In(05) vmx VMXVmdb: Local connection timeout: 60000 ms.
2024-01-20T08:10:04.346Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2024-01-20T08:10:04.346Z In(05) vmx Vix: [mainDispatch.c:486]: VMAutomation: Initializing VMAutomation.
2024-01-20T08:10:04.347Z In(05) vmx Vix: [mainDispatch.c:738]: VMAutomationOpenListenerSocket() listening
2024-01-20T08:10:04.355Z In(05) vmx Vix: [mainDispatch.c:4206]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2024-01-20T08:10:04.355Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2024-01-20T08:10:04.355Z In(05) vmx Vix: [mainDispatch.c:4206]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-01-20T08:10:04.355Z In(05) vmx Vix: [mainDispatch.c:4206]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2024-01-20T08:10:04.355Z In(05) vmx Vix: [mainDispatch.c:4206]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2024-01-20T08:10:04.408Z In(05) vmx IOPL_VBSRunning: VBS is set to 0
2024-01-20T08:10:04.410Z Wa(03) vmx Can't set WSS, error 1314
2024-01-20T08:10:04.410Z In(05) vmx VerificationOfHostParameters status 1
2024-01-20T08:10:04.410Z Wa(03) vmx VMX can't verify host parameters.
2024-01-20T08:10:04.410Z In(05) vmx FeatureCompat: No EVC masks.
2024-01-20T08:10:04.476Z In(05) vmx hostCPUID vendor: AuthenticAMD
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID family: 0x10 model: 0x4 stepping: 0x3
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID codename: Shanghai (K10)
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID name: AMD Phenom(tm) II X4 945 Processor
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 00000000, 0: 0x00000005 0x68747541 0x444d4163 0x69746e65
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 00000001, 0: 0x00100f43 0x00040800 0x00802009 0x178bfbff
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 00000002, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 00000004, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000000, 0: 0x8000001b 0x68747541 0x444d4163 0x69746e65
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000001, 0: 0x00100f43 0x10001ad6 0x000037ff 0xefd3fbff
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000002, 0: 0x20444d41 0x6e656850 0x74286d6f 0x4920296d
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000003, 0: 0x34582049 0x35343920 0x6f725020 0x73736563
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000004, 0: 0x0000726f 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000005, 0: 0xff30ff10 0xff30ff20 0x40020140 0x40020140
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000006, 0: 0x20800000 0x42004200 0x02008140 0x0030b140
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x000001f9
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000008, 0: 0x00003030 0x00000000 0x00002003 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 8000000a, 0: 0x00000001 0x00000040 0x00000000 0x0000000f
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 8000000b, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 8000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 8000000d, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 8000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 8000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000012, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000014, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000015, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000016, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000017, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000018, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 80000019, 0: 0xf0300000 0x60100000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 8000001a, 0: 0x00000003 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx hostCPUID level 8000001b, 0: 0x0000001f 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:04.477Z In(05) vmx CPUID differences from hostCPUID.
2024-01-20T08:10:04.477Z In(05) vmx Physical APIC IDs: 0-3
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR       0x3a =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x480 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x481 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x482 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x483 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x484 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x485 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x486 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x487 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x488 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x489 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x48a =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x48b =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x48c =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x48d =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x48e =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x48f =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x490 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x491 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR      0x492 =                  0
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR 0xc0010114 =                0x8
2024-01-20T08:10:04.477Z In(05) vmx Common: MSR       0xce =         0x80000000
2024-01-20T08:10:04.478Z In(05) vmx Common: MSR      0x10a =                  0
2024-01-20T08:10:04.478Z In(05) vmx Common: MSR      0x122 =                  0
2024-01-20T08:10:04.478Z In(05) vmx VMMon_GetkHzEstimate: Calculated 3010047 kHz
2024-01-20T08:10:04.478Z In(05) vmx TSC kHz estimates: vmmon 3010047, remembered 0, osReported 3000000. Using 3010047 kHz.
2024-01-20T08:10:04.478Z In(05) vmx TSC first measured delta 871
2024-01-20T08:10:04.478Z In(05) vmx TSC min delta 646
2024-01-20T08:10:04.478Z In(05) vmx PTSC: RefClockToPTSC 0 @ 10000000Hz -> 0 @ 3010047000Hz
2024-01-20T08:10:04.478Z In(05) vmx PTSC: RefClockToPTSC ((x * 2525010434) >> 23) + -1229735080636
2024-01-20T08:10:04.478Z In(05) vmx PTSC: tscOffset -1295832091874
2024-01-20T08:10:04.478Z In(05) vmx PTSC: using TSC
2024-01-20T08:10:04.478Z In(05) vmx PTSC: hardware TSCs are synchronized.
2024-01-20T08:10:04.478Z In(05) vmx PTSC: hardware TSCs may have been adjusted by the host.
2024-01-20T08:10:04.478Z In(05) vmx PTSC: current PTSC=320817
2024-01-20T08:10:04.503Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 1048
2024-01-20T08:10:04.571Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2024-01-20T08:10:04.571Z In(05) vmx changing directory to F:\vm_lonele\.
2024-01-20T08:10:04.572Z In(05) vmx Config file: F:\vm_lonele\Windows 7 的克隆.vmx
2024-01-20T08:10:04.572Z In(05) vmx Vix: [mainDispatch.c:4206]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-01-20T08:10:04.572Z In(05) vmx Vix: [mainDispatch.c:4206]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2024-01-20T08:10:04.661Z Wa(03) vmx PowerOn
2024-01-20T08:10:04.661Z In(05) vmx VMX_PowerOn: VMX build 18811642, UI build 18811642
2024-01-20T08:10:04.661Z In(05) vmx HostWin32: WIN32 NUMA node 0, CPU mask 0x000000000000000f
2024-01-20T08:10:04.676Z In(05) vmx Vix: [mainDispatch.c:4206]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2024-01-20T08:10:04.685Z In(05) vmx VMXSTATS: Successfully created statsfile: vm.scoreboard
2024-01-20T08:10:04.685Z In(05) vmx VMXSTATS: Update Product Information: VMware Workstation	16.2.1	build-18811642	Release  TotalBlockSize: 64
2024-01-20T08:10:04.685Z In(05) vmx HOST Windows version 10.0, build 22000, platform 2, ""
2024-01-20T08:10:04.685Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini
2024-01-20T08:10:04.685Z In(05) vmx DICT          printers.enabled = "FALSE"
2024-01-20T08:10:04.685Z In(05) vmx DICT --- NON PERSISTENT (null)
2024-01-20T08:10:04.685Z In(05) vmx DICT --- USER PREFERENCES C:\Users\<USER>\AppData\Roaming\VMware\preferences.ini
2024-01-20T08:10:04.685Z In(05) vmx DICT  pref.sharedFolder.maxNum = "42"
2024-01-20T08:10:04.685Z In(05) vmx DICT pref.sharedFolder0.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:04.685Z In(05) vmx DICT pref.sharedFolder0.guestName = "CH"
2024-01-20T08:10:04.685Z In(05) vmx DICT pref.sharedFolder0.hostPath = "D:\cdesktop\SMOS-II Setup For Win7 64(1)\SMOS-II Setup For Win7 64\SMOS-II Server Setup\SetupStep[7]_SMOS_II_Server_Setup\CH"
2024-01-20T08:10:04.685Z In(05) vmx DICT pref.sharedFolder0.enabled = "TRUE"
2024-01-20T08:10:04.685Z In(05) vmx DICT pref.sharedFolder1.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder1.guestName = "Downloads"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder1.hostPath = "C:\Users\<USER>\Downloads"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder1.enabled = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder2.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder2.guestName = "H"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder2.hostPath = "H:\"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder2.enabled = "FALSE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder3.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder3.guestName = "SMOS-II-CH V3.2"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder3.hostPath = "D:\u盘优盘\SMOS-II-CH V3.2"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder3.enabled = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window.count = "1"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.tab.count = "4"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.tab0.cnxType = "vmdb"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.tab1.cnxType = "vmdb"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.sidebar = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.sidebar.width = "200"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.statusBar = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.tabs = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar = "FALSE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.size = "100"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.view = "opened-vms"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.placement.left = "0"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.placement.top = "704"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.placement.right = "1300"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.placement.bottom = "1748"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.maximized = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT         vmWizard.guestKey = "windows7"
2024-01-20T08:10:04.686Z In(05) vmx DICT vmWizard.installMediaType = "later"
2024-01-20T08:10:04.686Z In(05) vmx DICT vmWizard.isoLocationMRU.count = "8"
2024-01-20T08:10:04.686Z In(05) vmx DICT vmWizard.isoLocationMRU0.location = "C:\Users\<USER>\Videos\Win10_22H2_Chinese_Simplified_x32v1.iso"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.tab2.cnxType = "vmdb"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder4.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder4.guestName = "cdesktop"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder4.hostPath = "D:\cdesktop"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder4.enabled = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder5.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder5.guestName = "ff"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder5.hostPath = "D:\u盘优盘"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder5.enabled = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder6.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder6.guestName = "软件"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder6.hostPath = "I:\软件"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder6.enabled = "FALSE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.snapshotManager.left = "74"
2024-01-20T08:10:04.686Z In(05) vmx DICT  pref.snapshotManager.top = "474"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.snapshotManager.right = "828"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.snapshotManager.bottom = "1027"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.tab3.cnxType = "vmdb"
2024-01-20T08:10:04.686Z In(05) vmx DICT vmWizard.isoLocationMRU1.location = "I:\W81PEAV\W81PEAV.iso"
2024-01-20T08:10:04.686Z In(05) vmx DICT vmWizard.isoLocationMRU2.location = "I:\[neubt]office2003卸载修复\干净PE系统合辑\无垠PE\无垠Win8PE64维护版V20181218.iso"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder7.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder7.guestName = "Downloads"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder7.hostPath = "D:\Users\bfit\Downloads"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder7.enabled = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder8.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder8.guestName = "F"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder8.hostPath = "F:\"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder8.enabled = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder9.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder9.guestName = "H"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder9.hostPath = "H:\"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder9.enabled = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder10.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder10.guestName = "SMOS-II-CH V3.2"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder10.hostPath = "D:\cdesktop\SMOS-II-CH V3.2"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder10.enabled = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder11.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder11.guestName = "chromeDownload"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder11.hostPath = "I:\chromeDownload"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder11.enabled = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder12.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder12.guestName = "软件"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder12.hostPath = "I:\软件"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder12.enabled = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder13.vmPath = "/vm/#46a795c643847c12/"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder13.guestName = "Framework462"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder13.hostPath = "C:\Users\<USER>\Downloads\Framework462"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder13.enabled = "TRUE"
2024-01-20T08:10:04.686Z In(05) vmx DICT vmWizard.isoLocationMRU3.location = "F:\Win10_22H2_Chinese_Simplified_x64v1.iso"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.ws.session.window0.tab4.cnxType = "vmdb"
2024-01-20T08:10:04.686Z In(05) vmx DICT pref.sharedFolder14.vmPath = "/vm/#46a795c643847c12/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder14.guestName = "u盘优盘"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder14.hostPath = "D:\u盘优盘"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder14.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder15.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder15.guestName = "Framework462"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder15.hostPath = "C:\Users\<USER>\Downloads\Framework462"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder15.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder16.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder16.guestName = "H"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder16.hostPath = "H:\"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder16.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder17.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder17.guestName = "HRSword5.0.1.1 (1)"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder17.hostPath = "C:\Users\<USER>\Downloads\HRSword5.0.1.1 (1)"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder17.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder18.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder18.guestName = "Sysdiag"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder18.hostPath = "C:\Users\<USER>\Downloads\HRSword2021.01.25\HRSword"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder18.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder19.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder19.guestName = "Sysdiag"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder19.hostPath = "D:\software\共享\Sysdiag"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder19.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder20.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder20.guestName = "SysinternalsSuite"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder20.hostPath = "I:\chromeDownload\SysinternalsSuite"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder20.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder21.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder21.guestName = "cdesktop"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder21.hostPath = "D:\cdesktop"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder21.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT vmWizard.isoLocationMRU4.location = "D:\amd_m2-xplus\Windows XP SP3\windows_xp_professional_with_service_pack_3.iso"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder22.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder22.guestName = "chromeDownload"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder22.hostPath = "I:\chromeDownload"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder22.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder23.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder23.guestName = "Framework462"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder23.hostPath = "C:\Users\<USER>\Downloads\Framework462"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder23.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder24.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder24.guestName = "SMOS-II-CH V3.0"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder24.hostPath = "D:\SMOS-II-CH V3.0-三菱电梯\SMOS-II-CH V3.0"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder24.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder25.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder25.guestName = "SMOS-II-CH V3.0"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder25.hostPath = "D:\SMOS-II-CH V3.0-涓夎彵鐢垫\SMOS-II-CH V3.0"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder25.enabled = "FALSE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder26.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder26.guestName = "SMOS-II-CH V3.2"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder26.hostPath = "D:\cdesktop\SMOS-II-CH V3.2"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder26.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder27.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder27.guestName = "VirtualKD-Redux-2020.0"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder27.hostPath = "C:\Users\<USER>\Downloads\VirtualKD-Redux-2020.0"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder27.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder28.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder28.guestName = "u盘优盘"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder28.hostPath = "D:\u盘优盘"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder28.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder29.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder29.guestName = "电梯综合监控系统"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder29.hostPath = "C:\Program Files (x86)\上海三菱电梯有限公司\电梯综合监控系统"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder29.enabled = "TRUE"
2024-01-20T08:10:04.687Z In(05) vmx DICT vmWizard.isoLocationMRU5.location = "H:\VMware\旧经典版\V11.1.0\tools\windows.iso"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder30.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder30.guestName = "鐢垫缁煎悎鐩戞帶绯荤粺"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder30.hostPath = "C:\Program Files (x86)\涓婃捣涓夎彵鐢垫鏈夐檺鍏徃\鐢垫缁煎悎鐩戞帶绯荤粺"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder30.enabled = "FALSE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder31.vmPath = "/vm/#a1208ea096a895f7/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder31.guestName = "Downloads"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder31.hostPath = "D:\chromeDownload\JDownloaderPortable\Data\Downloads"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder31.enabled = "FALSE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder32.vmPath = "/vm/#a1208ea096a895f7/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder32.guestName = "Vbsedit5.2.4"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder32.hostPath = "C:\Vbsedit5.2.4"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder32.enabled = "FALSE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder33.vmPath = "/vm/#a1208ea096a895f7/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder33.guestName = "awms-duibi"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder33.hostPath = "D:\awms-duibi"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder33.enabled = "FALSE"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.ws.session.window0.tab5.cnxType = "vmdb"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder34.vmPath = "/vm/#a1208ea096a895f7/"
2024-01-20T08:10:04.687Z In(05) vmx DICT pref.sharedFolder34.guestName = "考勤demo"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder34.hostPath = "I:\考勤demo"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder34.enabled = "FALSE"
2024-01-20T08:10:04.688Z In(05) vmx DICT vmWizard.isoLocationMRU6.location = "\\*************\历史资料库\10、常用软件\03、系统软件\win7\win7_sp1_.iso"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab6.cnxType = "vmdb"
2024-01-20T08:10:04.688Z In(05) vmx DICT vmWizard.isoLocationMRU7.location = "D:\u盘优盘\cn_windows_7_professional_with_sp1_vl_build_x64_dvd_u_677816_Driver.iso"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder35.vmPath = "/vm/#d04ddfc72e8e7fb9/"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder35.guestName = "Framework462"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder35.hostPath = "C:\Users\<USER>\Downloads\Framework462"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder35.enabled = "FALSE"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder36.vmPath = "/vm/#d04ddfc72e8e7fb9/"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder36.guestName = "SMOS-II-CH V3.0"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder36.hostPath = "D:\SMOS-II-CH V3.0-涓夎彵鐢垫\SMOS-II-CH V3.0"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder36.enabled = "FALSE"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder37.vmPath = "/vm/#d04ddfc72e8e7fb9/"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder37.guestName = "SMOS-II-CH V3.2"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder37.hostPath = "D:\cdesktop\SMOS-II-CH V3.2"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder37.enabled = "FALSE"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder38.vmPath = "/vm/#d04ddfc72e8e7fb9/"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder38.guestName = "VirtualKD-Redux-2020.0"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder38.hostPath = "C:\Users\<USER>\Downloads\VirtualKD-Redux-2020.0"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder38.enabled = "FALSE"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder39.vmPath = "/vm/#d04ddfc72e8e7fb9/"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder39.guestName = "鐢垫缁煎悎鐩戞帶绯荤粺"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder39.hostPath = "C:\Program Files (x86)\涓婃捣涓夎彵鐢垫鏈夐檺鍏徃\鐢垫缁煎悎鐩戞帶绯荤粺"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder39.enabled = "FALSE"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder40.vmPath = "/vm/#e03ebf42476068a7/"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder40.guestName = "H"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder40.hostPath = "H:\"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder40.enabled = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab0.dest = ""
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab0.file = ""
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab0.type = "home"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab0.focused = "FALSE"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab1.dest = ""
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab1.file = "F:\vm_lonele\Windows 7 的克隆.vmx"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab1.type = "vm"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab1.focused = "FALSE"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder41.vmPath = "/vm/#e03ebf42476068a7/"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder41.guestName = "软件"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder41.hostPath = "I:\软件"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.sharedFolder41.enabled = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab2.dest = ""
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab2.file = "F:\bf_lonele\Windows 7.vmx"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab2.type = "vm"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab2.focused = "FALSE"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab3.dest = ""
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab3.file = "I:\vmware-awms\Windows Server 2008 R2 x64.vmx"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab3.type = "vm"
2024-01-20T08:10:04.688Z In(05) vmx DICT pref.ws.session.window0.tab3.focused = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2024-01-20T08:10:04.688Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2024-01-20T08:10:04.688Z In(05) vmx DICT         authd.client.port = "902"
2024-01-20T08:10:04.688Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-01-20T08:10:04.688Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "no"
2024-01-20T08:10:04.688Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "30879"
2024-01-20T08:10:04.688Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-01-20T08:10:04.688Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "no"
2024-01-20T08:10:04.688Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "30879"
2024-01-20T08:10:04.688Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2024-01-20T08:10:04.688Z In(05) vmx DICT         authd.client.port = "902"
2024-01-20T08:10:04.688Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-01-20T08:10:04.688Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "no"
2024-01-20T08:10:04.688Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "30879"
2024-01-20T08:10:04.688Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-01-20T08:10:04.688Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "no"
2024-01-20T08:10:04.688Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "30879"
2024-01-20T08:10:04.688Z In(05) vmx DICT --- NONPERSISTENT
2024-01-20T08:10:04.688Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT             gui.available = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT --- COMMAND LINE
2024-01-20T08:10:04.688Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT             gui.available = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT --- RECORDING
2024-01-20T08:10:04.688Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT             gui.available = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT --- CONFIGURATION F:\vm_lonele\Windows 7 的克隆.vmx 
2024-01-20T08:10:04.688Z In(05) vmx DICT            config.version = "8"
2024-01-20T08:10:04.688Z In(05) vmx DICT         virtualHW.version = "19"
2024-01-20T08:10:04.688Z In(05) vmx DICT              mks.enable3d = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2024-01-20T08:10:04.688Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2024-01-20T08:10:04.688Z In(05) vmx DICT      pciBridge4.functions = "8"
2024-01-20T08:10:04.688Z In(05) vmx DICT        pciBridge5.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT     pciBridge5.virtualDev = "pcieRootPort"
2024-01-20T08:10:04.689Z In(05) vmx DICT      pciBridge5.functions = "8"
2024-01-20T08:10:04.689Z In(05) vmx DICT        pciBridge6.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT     pciBridge6.virtualDev = "pcieRootPort"
2024-01-20T08:10:04.689Z In(05) vmx DICT      pciBridge6.functions = "8"
2024-01-20T08:10:04.689Z In(05) vmx DICT        pciBridge7.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT     pciBridge7.virtualDev = "pcieRootPort"
2024-01-20T08:10:04.689Z In(05) vmx DICT      pciBridge7.functions = "8"
2024-01-20T08:10:04.689Z In(05) vmx DICT             vmci0.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT             hpet0.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT                     nvram = "Windows 7 的克隆.nvram"
2024-01-20T08:10:04.689Z In(05) vmx DICT virtualHW.productCompatibility = "hosted"
2024-01-20T08:10:04.689Z In(05) vmx DICT        powerType.powerOff = "soft"
2024-01-20T08:10:04.689Z In(05) vmx DICT         powerType.powerOn = "soft"
2024-01-20T08:10:04.689Z In(05) vmx DICT         powerType.suspend = "soft"
2024-01-20T08:10:04.689Z In(05) vmx DICT           powerType.reset = "soft"
2024-01-20T08:10:04.689Z In(05) vmx DICT               displayName = "Windows 10 lonele"
2024-01-20T08:10:04.689Z In(05) vmx DICT usb.vbluetooth.startConnected = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT                   guestOS = "windows7"
2024-01-20T08:10:04.689Z In(05) vmx DICT            tools.syncTime = "FALSE"
2024-01-20T08:10:04.689Z In(05) vmx DICT          sound.autoDetect = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT          sound.virtualDev = "hdaudio"
2024-01-20T08:10:04.689Z In(05) vmx DICT            sound.fileName = "-1"
2024-01-20T08:10:04.689Z In(05) vmx DICT             sound.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT                   memsize = "2048"
2024-01-20T08:10:04.689Z In(05) vmx DICT                mem.hotadd = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT          scsi0.virtualDev = "lsisas1068"
2024-01-20T08:10:04.689Z In(05) vmx DICT             scsi0.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT             sata0.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT          scsi0:0.fileName = "Windows 7-cl1-000002.vmdk"
2024-01-20T08:10:04.689Z In(05) vmx DICT        sata0:1.deviceType = "cdrom-image"
2024-01-20T08:10:04.689Z In(05) vmx DICT          sata0:1.fileName = "C:\Users\<USER>\Videos\Win10_22H2_Chinese_Simplified_x32v1.iso"
2024-01-20T08:10:04.689Z In(05) vmx DICT           sata0:1.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT               usb.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT              ehci.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT     svga.graphicsMemoryKB = "8388608"
2024-01-20T08:10:04.689Z In(05) vmx DICT  ethernet0.connectionType = "nat"
2024-01-20T08:10:04.689Z In(05) vmx DICT     ethernet0.addressType = "generated"
2024-01-20T08:10:04.689Z In(05) vmx DICT      ethernet0.virtualDev = "e1000"
2024-01-20T08:10:04.689Z In(05) vmx DICT          serial0.fileType = "thinprint"
2024-01-20T08:10:04.689Z In(05) vmx DICT          serial0.fileName = "thinprint"
2024-01-20T08:10:04.689Z In(05) vmx DICT         ethernet0.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT           serial0.present = "TRUE"
2024-01-20T08:10:04.689Z In(05) vmx DICT        extendedConfigFile = "Windows 7 的克隆.vmxf"
2024-01-20T08:10:04.689Z In(05) vmx DICT      numa.autosize.cookie = "10012"
2024-01-20T08:10:04.689Z In(05) vmx DICT numa.autosize.vcpu.maxPerVirtualNode = "1"
2024-01-20T08:10:04.689Z In(05) vmx DICT                 uuid.bios = "56 4d 1b fb d1 ef e5 0c-eb 4e 61 52 46 e3 cd 10"
2024-01-20T08:10:04.689Z In(05) vmx DICT             uuid.location = "56 4d 1b fb d1 ef e5 0c-eb 4e 61 52 46 e3 cd 10"
2024-01-20T08:10:04.689Z In(05) vmx DICT              scsi0:0.redo = ""
2024-01-20T08:10:04.689Z In(05) vmx DICT  pciBridge0.pciSlotNumber = "17"
2024-01-20T08:10:04.689Z In(05) vmx DICT  pciBridge4.pciSlotNumber = "21"
2024-01-20T08:10:04.689Z In(05) vmx DICT  pciBridge5.pciSlotNumber = "22"
2024-01-20T08:10:04.689Z In(05) vmx DICT  pciBridge6.pciSlotNumber = "23"
2024-01-20T08:10:04.689Z In(05) vmx DICT  pciBridge7.pciSlotNumber = "24"
2024-01-20T08:10:04.689Z In(05) vmx DICT       scsi0.pciSlotNumber = "160"
2024-01-20T08:10:04.689Z In(05) vmx DICT         usb.pciSlotNumber = "32"
2024-01-20T08:10:04.689Z In(05) vmx DICT   ethernet0.pciSlotNumber = "33"
2024-01-20T08:10:04.689Z In(05) vmx DICT       sound.pciSlotNumber = "34"
2024-01-20T08:10:04.689Z In(05) vmx DICT        ehci.pciSlotNumber = "35"
2024-01-20T08:10:04.689Z In(05) vmx DICT       sata0.pciSlotNumber = "36"
2024-01-20T08:10:04.689Z In(05) vmx DICT             scsi0.sasWWID = "50 05 05 6b d1 ef e5 00"
2024-01-20T08:10:04.689Z In(05) vmx DICT             svga.vramSize = "268435456"
2024-01-20T08:10:04.689Z In(05) vmx DICT  vmotion.checkpointFBSize = "8388608"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.checkpointSVGAPrimarySize = "268435456"
2024-01-20T08:10:04.689Z In(05) vmx DICT   vmotion.svga.mobMaxSize = "1073741824"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.graphicsMemoryKB = "8388608"
2024-01-20T08:10:04.689Z In(05) vmx DICT   vmotion.svga.supports3D = "1"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.baseCapsLevel = "9"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.maxPointSize = "1"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.maxTextureSize = "16384"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.maxVolumeExtent = "2048"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.maxTextureAnisotropy = "16"
2024-01-20T08:10:04.689Z In(05) vmx DICT  vmotion.svga.lineStipple = "0"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.dxMaxConstantBuffers = "14"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.dxProvokingVertex = "0"
2024-01-20T08:10:04.689Z In(05) vmx DICT         vmotion.svga.sm41 = "1"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.multisample2x = "1"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.multisample4x = "1"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.msFullQuality = "1"
2024-01-20T08:10:04.689Z In(05) vmx DICT     vmotion.svga.logicOps = "1"
2024-01-20T08:10:04.689Z In(05) vmx DICT         vmotion.svga.bc67 = "9"
2024-01-20T08:10:04.689Z In(05) vmx DICT          vmotion.svga.sm5 = "1"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.multisample8x = "1"
2024-01-20T08:10:04.689Z In(05) vmx DICT vmotion.svga.logicBlendOps = "1"
2024-01-20T08:10:04.689Z In(05) vmx DICT ethernet0.generatedAddress = "00:0c:29:e3:cd:10"
2024-01-20T08:10:04.689Z In(05) vmx DICT ethernet0.generatedAddressOffset = "0"
2024-01-20T08:10:04.689Z In(05) vmx DICT                  vmci0.id = "-37635189"
2024-01-20T08:10:04.689Z In(05) vmx DICT    monitor.phys_bits_used = "45"
2024-01-20T08:10:04.689Z In(05) vmx DICT             cleanShutdown = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT              softPowerOff = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT               usb:1.speed = "2"
2024-01-20T08:10:04.690Z In(05) vmx DICT             usb:1.present = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT          usb:1.deviceType = "hub"
2024-01-20T08:10:04.690Z In(05) vmx DICT                usb:1.port = "1"
2024-01-20T08:10:04.690Z In(05) vmx DICT              usb:1.parent = "-1"
2024-01-20T08:10:04.690Z In(05) vmx DICT isolation.tools.hgfs.disable = "FALSE"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder0.present = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder0.enabled = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder0.readAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT sharedFolder0.writeAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT    sharedFolder0.hostPath = "H:\"
2024-01-20T08:10:04.690Z In(05) vmx DICT   sharedFolder0.guestName = "H"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder0.expiration = "never"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder1.present = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder1.enabled = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder1.readAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT sharedFolder1.writeAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT    sharedFolder1.hostPath = "I:\软件"
2024-01-20T08:10:04.690Z In(05) vmx DICT   sharedFolder1.guestName = "软件"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder1.expiration = "never"
2024-01-20T08:10:04.690Z In(05) vmx DICT       sharedFolder.maxNum = "6"
2024-01-20T08:10:04.690Z In(05) vmx DICT       tools.remindInstall = "FALSE"
2024-01-20T08:10:04.690Z In(05) vmx DICT           ide0:0.fileName = "Windows 7.vmdk"
2024-01-20T08:10:04.690Z In(05) vmx DICT               ide0:0.redo = ""
2024-01-20T08:10:04.690Z In(05) vmx DICT           scsi0:0.present = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT toolsInstallManager.lastInstallError = "0"
2024-01-20T08:10:04.690Z In(05) vmx DICT   guestInfo.detailed.data = <not printed>
2024-01-20T08:10:04.690Z In(05) vmx DICT toolsInstallManager.updateCounter = "2"
2024-01-20T08:10:04.690Z In(05) vmx DICT svga.guestBackedPrimaryAware = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT        checkpoint.vmState = ""
2024-01-20T08:10:04.690Z In(05) vmx DICT                   vc.uuid = ""
2024-01-20T08:10:04.690Z In(05) vmx DICT          policy.vm.mvmtid = ""
2024-01-20T08:10:04.690Z In(05) vmx DICT   gui.lastPoweredViewMode = "fullscreen"
2024-01-20T08:10:04.690Z In(05) vmx DICT    sata0:1.startConnected = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder2.present = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder2.enabled = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder2.readAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT sharedFolder2.writeAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT    sharedFolder2.hostPath = "D:\cdesktop\SMOS-II Setup For Win7 64(1)\SMOS-II Setup For Win7 64\SMOS-II Server Setup\SetupStep[7]_SMOS_II_Server_Setup\CH"
2024-01-20T08:10:04.690Z In(05) vmx DICT   sharedFolder2.guestName = "CH"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder2.expiration = "never"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder3.present = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder3.enabled = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder3.readAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT sharedFolder3.writeAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT    sharedFolder3.hostPath = "D:\u盘优盘"
2024-01-20T08:10:04.690Z In(05) vmx DICT   sharedFolder3.guestName = "ff"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder3.expiration = "session"
2024-01-20T08:10:04.690Z In(05) vmx DICT           floppy0.present = "FALSE"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder4.present = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder4.enabled = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder4.readAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT sharedFolder4.writeAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT    sharedFolder4.hostPath = "D:\cdesktop"
2024-01-20T08:10:04.690Z In(05) vmx DICT   sharedFolder4.guestName = "cdesktop"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder4.expiration = "never"
2024-01-20T08:10:04.690Z In(05) vmx DICT                vhv.enable = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT      tools.upgrade.policy = "useGlobal"
2024-01-20T08:10:04.690Z In(05) vmx DICT             usb:0.present = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT          usb:0.deviceType = "hid"
2024-01-20T08:10:04.690Z In(05) vmx DICT                usb:0.port = "0"
2024-01-20T08:10:04.690Z In(05) vmx DICT              usb:0.parent = "-1"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder5.present = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT     sharedFolder5.enabled = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder5.readAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT sharedFolder5.writeAccess = "TRUE"
2024-01-20T08:10:04.690Z In(05) vmx DICT    sharedFolder5.hostPath = "C:\Users\<USER>\Downloads"
2024-01-20T08:10:04.690Z In(05) vmx DICT   sharedFolder5.guestName = "Downloads"
2024-01-20T08:10:04.690Z In(05) vmx DICT  sharedFolder5.expiration = "never"
2024-01-20T08:10:04.690Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini 
2024-01-20T08:10:04.690Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2024-01-20T08:10:04.690Z In(05) vmx DICT         authd.client.port = "902"
2024-01-20T08:10:04.690Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-01-20T08:10:04.690Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "no"
2024-01-20T08:10:04.690Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "30879"
2024-01-20T08:10:04.690Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-01-20T08:10:04.690Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "no"
2024-01-20T08:10:04.690Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "30879"
2024-01-20T08:10:04.690Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2024-01-20T08:10:04.690Z In(05) vmx DICT         authd.client.port = "902"
2024-01-20T08:10:04.690Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-01-20T08:10:04.690Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "no"
2024-01-20T08:10:04.690Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "30879"
2024-01-20T08:10:04.690Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-01-20T08:10:04.690Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "no"
2024-01-20T08:10:04.690Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "30879"
2024-01-20T08:10:04.690Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini 
2024-01-20T08:10:04.690Z In(05) vmx DICT          printers.enabled = "FALSE"
2024-01-20T08:10:04.691Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-01-20T08:10:04.691Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-01-20T08:10:04.692Z In(05) vmx ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-01-20T08:10:04.692Z In(05) vmx ToolsISO: Selected Tools ISO 'windows.iso' for 'windows7' guest.
2024-01-20T08:10:04.693Z In(05) vmx Vix: [mainDispatch.c:4206]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-01-20T08:10:04.695Z In(05) vmx Monitor Mode: CPL0
2024-01-20T08:10:04.728Z In(05) vmx OvhdMem_PowerOn: initial admission: paged  3957055 nonpaged     5203 anonymous     8068
2024-01-20T08:10:04.728Z In(05) vmx VMMEM: Initial Reservation: 15509MB (MainMem=2048MB)
2024-01-20T08:10:04.728Z In(05) vmx MemSched_PowerOn: balloon minGuestSize 209715 (80% of min required size 262144)
2024-01-20T08:10:04.728Z In(05) vmx MemSched: reserved mem (in MB) min 128 max 16128 recommended 16128
2024-01-20T08:10:04.728Z In(05) vmx MemSched: pg 3957055 np 5203 anon 8068 mem 524288
2024-01-20T08:10:04.745Z In(05) vmx MemSched: numvm 1 locked pages: num 0 max 4120576
2024-01-20T08:10:04.746Z In(05) vmx MemSched: locked Page Limit: host 4493321 config 4128768
2024-01-20T08:10:04.746Z In(05) vmx MemSched: minmempct 50 minalloc 0 admitted 1
2024-01-20T08:10:04.746Z In(05) vmx llc: maximum vcpus per LLC: 1
2024-01-20T08:10:04.746Z In(05) vmx llc: vLLC size: 1
2024-01-20T08:10:04.747Z In(05) PowerNotifyThread VTHREAD 14484 "PowerNotifyThread"
2024-01-20T08:10:04.747Z In(05) PowerNotifyThread PowerNotify thread is alive.
2024-01-20T08:10:04.747Z In(05) vmx VMXSTATS: Registering 1 stats: vmx.logDropChars
2024-01-20T08:10:04.747Z In(05) vmx VMXSTATS: Registering 2 stats: vmx.logBytesLogged
2024-01-20T08:10:04.747Z In(05) vmx VMXSTATS: Registering 3 stats: vmx.numTimesLogDrop
2024-01-20T08:10:04.749Z In(05) vmx LICENSE using: 'HKEY_LOCAL_MACHINE\SOFTWARE\VMware, Inc.\VMware Workstation\Dormant\License.ws.16.0.e5.202001' 
2024-01-20T08:10:04.750Z In(05) vthread-5020 VTHREAD 5020 "vthread-5020"
2024-01-20T08:10:04.751Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("F:\vm_lonele\Windows 7 的克隆.vmpl", ...) failed, error: 2
2024-01-20T08:10:04.751Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2024-01-20T08:10:04.751Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("F:\vm_lonele\Windows 7 的克隆.vmpl", ...) failed, error: 2
2024-01-20T08:10:04.751Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2024-01-20T08:10:04.752Z In(05) vmx Host PA size: 48 bits. Guest PA size: 45 bits.
2024-01-20T08:10:04.753Z In(05) vmx ToolsISO: Refreshing imageName for 'windows7' (refreshCount=1, lastCount=1).
2024-01-20T08:10:04.753Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-01-20T08:10:04.753Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-01-20T08:10:04.754Z In(05) vmx ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-01-20T08:10:04.754Z In(05) vmx ToolsISO: Selected Tools ISO 'windows.iso' for 'windows7' guest.
2024-01-20T08:10:04.755Z In(05) deviceThread VTHREAD 7740 "deviceThread"
2024-01-20T08:10:04.755Z In(05) deviceThread Device thread is alive
2024-01-20T08:10:04.755Z In(05) vmx Host AMD-V Capabilities:
2024-01-20T08:10:04.755Z In(05) vmx SVM Revision:           1
2024-01-20T08:10:04.755Z In(05) vmx ASIDs Supported:        64
2024-01-20T08:10:04.755Z In(05) vmx Nested Paging:          yes
2024-01-20T08:10:04.755Z In(05) vmx SVM GMET:               no
2024-01-20T08:10:04.755Z In(05) vmx LBR Virtualization:     yes
2024-01-20T08:10:04.755Z In(05) vmx SVM Lock:               yes
2024-01-20T08:10:04.755Z In(05) vmx NRIP Save:              yes
2024-01-20T08:10:04.755Z In(05) vmx TSC Rate MSR:           no
2024-01-20T08:10:04.756Z In(05) vmx VMCB Clean Bits:        no
2024-01-20T08:10:04.756Z In(05) vmx Flush by ASID:          no
2024-01-20T08:10:04.756Z In(05) vmx Decode Assists:         no
2024-01-20T08:10:04.756Z In(05) vmx Pause Filter:           no
2024-01-20T08:10:04.756Z In(05) vmx Pause Filter Threshold: no
2024-01-20T08:10:04.756Z In(05) vmx Monitor_PowerOn: HostedVSMP skew tracking is disabled
2024-01-20T08:10:04.756Z In(05) vmx vmm-modules: [vmm.vmm, vmce-vmce.vmm, viommu-none.vmm, vprobe-none.vmm, hv-svm.vmm, gphys-npt.vmm, callstack-none.vmm, gi-none.vmm, gmm-none.vmm, ahci-ahci.vmm, !ahciRegs=0x0, !ahciShared=0x1000, !e1000Shared=0x1c00, !vmSamples=0x2480, !theIOSpace=0x24c0, !ttGPPerVcpu=0x8980, {UseUnwind}=0x0, numVCPUsAsAddr=0x1, {SharedAreaReservations}=0x89c0, {rodataSize}=0x23335, {textAddr}=0xfffffffffc000000, {textSize}=0x90ed2, <MonSrcFile>]
2024-01-20T08:10:04.756Z In(05) vmx vmm-vcpus:   1
2024-01-20T08:10:04.793Z In(05) vmx KHZEstimate 3010047
2024-01-20T08:10:04.793Z In(05) vmx MHZEstimate 3010
2024-01-20T08:10:04.793Z In(05) vmx NumVCPUs 1
2024-01-20T08:10:04.794Z In(05) vmx AIOGNRC: numThreads=17 ide=0, scsi=1, passthru=0
2024-01-20T08:10:04.794Z In(05) vmx WORKER: Creating new group with maxThreads=17 (17)
2024-01-20T08:10:04.810Z In(05) vmx WORKER: Creating new group with maxThreads=1 (18)
2024-01-20T08:10:04.810Z In(05) vmx MainMem: CPT Host WZ=0 PF=2048 D=0
2024-01-20T08:10:04.810Z In(05) vmx MainMem: CPT PLS=1 PLR=1 BS=1 BlkP=32 Mult=4 W=50
2024-01-20T08:10:04.813Z Wa(03) vmx FILE: FileLockMemberValues removing problematic lock file 'F:\vm_lonele\564d1bfb-d1ef-e50c-eb4e-615246e3cd10.vmem.lck\M61243.lck'
2024-01-20T08:10:04.815Z In(05) vmx MainMem: Opened paging file, 'F:\vm_lonele\564d1bfb-d1ef-e50c-eb4e-615246e3cd10.vmem'.
2024-01-20T08:10:04.815Z In(05) vmx MStat: Creating Stat vm.uptime
2024-01-20T08:10:04.815Z In(05) vmx MStat: Creating Stat vm.suspendTime
2024-01-20T08:10:04.815Z In(05) vmx MStat: Creating Stat vm.powerOnTimeStamp
2024-01-20T08:10:04.816Z In(05) aioCompletion VTHREAD 1984 "aioCompletion"
2024-01-20T08:10:04.816Z In(05) vmx VMXAIOMGR: Using: simple=Compl
2024-01-20T08:10:04.821Z In(05) vmx WORKER: Creating new group with maxThreads=1 (19)
2024-01-20T08:10:04.827Z In(05) vmx WORKER: Creating new group with maxThreads=14 (33)
2024-01-20T08:10:04.829Z In(05) vmx FeatureCompat: No VM masks.
2024-01-20T08:10:04.829Z In(05) vmx TimeTracker host to guest rate conversion 1056204096 @ 3010047000Hz -> 0 @ 3010047000Hz
2024-01-20T08:10:04.829Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + -1056204096
2024-01-20T08:10:04.829Z In(05) vmx Disabling TSC scaling since host does not support it.
2024-01-20T08:10:04.829Z In(05) vmx TSC offsetting enabled.
2024-01-20T08:10:04.829Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2024-01-20T08:10:04.829Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2024-01-20T08:10:04.829Z In(05) vmx MKS PowerOn
2024-01-20T08:10:04.831Z In(05) mks VTHREAD 12948 "mks"
2024-01-20T08:10:04.831Z In(05) mks MKS thread is alive
2024-01-20T08:10:04.831Z In(05) svga VTHREAD 3888 "svga"
2024-01-20T08:10:04.831Z In(05) svga SVGA thread is alive
2024-01-20T08:10:04.832Z In(05) mks MKS: SSE2=1, SSSE3=1, SSE4_1=1
2024-01-20T08:10:04.839Z In(05) mouse VTHREAD 16836 "mouse"
2024-01-20T08:10:04.839Z In(05) mks MKS-HookKeyboard: RegQueryValueEx(LowLevelHooksTimeout) failed: The system cannot find the file specified (2)
2024-01-20T08:10:04.840Z In(05) kbh VTHREAD 14500 "kbh"
2024-01-20T08:10:04.842Z In(05) mks MKS Win32: Registering top level window (0x60ca6) to receive session change notification.
2024-01-20T08:10:04.844Z In(05) mks Current Display Settings:
2024-01-20T08:10:04.844Z In(05) mks    Display: 0 size: 1280x1024  position: (0, 0) name: \\.\DISPLAY1  
2024-01-20T08:10:04.848Z In(05) mks MKS Win32: MIL: 0x4000
2024-01-20T08:10:04.848Z In(05) mks MKS-RenderMain: PowerOn allowed MKSBasicOps DX11Renderer DX11BasicRenderer 
2024-01-20T08:10:04.848Z In(05) mks MKS-RenderMain: ISB enabled by config
2024-01-20T08:10:04.848Z In(05) mks MKS-RenderMain: Collecting RenderOps caps from DX11Renderer
2024-01-20T08:10:04.848Z In(05) mks MKS-RenderMain: Starting ISBRenderer (DX11Renderer)
2024-01-20T08:10:04.849Z In(05) mks ISBRendererComm: ISBRendererComm DataChannel size=1073741824
2024-01-20T08:10:04.857Z In(05) mks ISBRendererComm: mksSandbox command-line: C:\Program Files (x86)\VMware\VMware Workstation\x64\mksSandbox.exe --pipeInfo \\.\pipe\vmware\mksSandbox\mksSandbox-52 d1 0c 23 ed 69 6e c6-cf 43 75 f0 18 a3 97 7b
2024-01-20T08:10:07.166Z In(05) mks ISBRendererComm: Spawned process with handle 1432
2024-01-20T08:10:07.334Z In(05) mks ISBRendererComm: Sandbox Renderer: DX11Renderer
2024-01-20T08:10:07.345Z In(05) mks MKS-RenderMain: Started ISBRenderer (DX11Renderer)
2024-01-20T08:10:07.345Z In(05) mks MKS-RenderMain: Found Full Renderer: ISBRenderer (DX11Renderer)
2024-01-20T08:10:07.345Z In(05) mks MKS-RenderMain: maxTextureSize=16384
2024-01-20T08:10:07.346Z In(05) mks KHBKL: Unable to parse keystring at: ''
2024-01-20T08:10:07.346Z In(05) mks MKSRemoteMgr: Set default display name: Windows 10 lonele
2024-01-20T08:10:07.346Z In(05) mks MKSRemoteMgr: Loading VNC Configuration from VM config file
2024-01-20T08:10:07.346Z In(05) mks MKSRemoteMgr: Using default VNC keymap table "us"
2024-01-20T08:10:07.347Z In(05) vmx VLANCE: send cluster threshold is 80, size = 2 recalcInterval is 20000 us
2024-01-20T08:10:07.347Z In(05) vmx VMXNET: send cluster threshold is 80, size = 2 recalcInterval is 20000 ticks, dontClusterSize is 128
2024-01-20T08:10:07.349Z In(05) vmx Chipset version: 0x13
2024-01-20T08:10:07.352Z In(05) vmx SOUNDLIB: Creating the Wave sound backend.
2024-01-20T08:10:07.386Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation begins.
2024-01-20T08:10:07.386Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation completes.
2024-01-20T08:10:07.387Z No(00) worker-5020 ConfigDB: Setting scsi0:0.redo = ""
2024-01-20T08:10:07.387Z In(05) worker-5020 DISK: OPEN scsi0:0 'F:\vm_lonele\Windows 7-cl1-000002.vmdk' 永久 R[]
2024-01-20T08:10:07.403Z Wa(03) worker-5020 FILE: FileLockMemberValues removing problematic lock file 'F:\vm_lonele\Windows 7-cl1-000002.vmdk.lck\M28178.lck'
2024-01-20T08:10:07.436Z Wa(03) worker-5020 FILE: FileLockMemberValues removing problematic lock file 'F:\vm_lonele\Windows 7-cl1-000001.vmdk.lck\M60815.lck'
2024-01-20T08:10:07.508Z Wa(03) worker-5020 FILE: FileLockMemberValues removing problematic lock file 'F:\vm_lonele\Windows 7-cl1.vmdk.lck\M62184.lck'
2024-01-20T08:10:07.539Z In(05) worker-5020 DISKLIB-LIB_MISC   : DiskLib_GetStorageBlockSizes: Failed to get storage block sizes, The virtual disk requires a feature not supported by this program.
2024-01-20T08:10:07.540Z In(05) worker-5020 DiskGetGeometry: Reading of disk partition table
2024-01-20T08:10:07.541Z In(05) worker-5020 DISK: Disk 'F:\vm_lonele\Windows 7-cl1-000002.vmdk' has UUID '60 00 c2 9c 37 50 03 35-e0 85 2f 30 27 ea c2 a5'
2024-01-20T08:10:07.541Z In(05) worker-5020 DISK: OPEN 'F:\vm_lonele\Windows 7-cl1-000002.vmdk' Geo (13054/255/63) BIOS Geo (13054/255/63)
2024-01-20T08:10:07.552Z In(05) worker-5020 DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Reported rotation rate = 5400
2024-01-20T08:10:07.552Z In(05) vmx DISK: Opening disks took 165 ms.
2024-01-20T08:10:07.553Z In(05) vmx USBArbLib: USBArbLib initialized successfully, retryIntervalStart(5), retryIntervalMax(120), arbSocketName(\\.\pipe\vmware-usbarbpipe), useLocking(yes), noSetStringVersion(no).
2024-01-20T08:10:07.553Z In(05) vmx UsbEnum: Initializing UsbEnum library, disableLocking(no), allowBootableHid(yes).
2024-01-20T08:10:07.553Z In(05) vmx USB: Initializing 'Virtual Hub' backend
2024-01-20T08:10:07.553Z In(05) vmx USB: Initializing 'Generic' backend
2024-01-20T08:10:07.553Z Wa(03) vmx USBArbLib: OUT SET_AUTO_CONNECT: Not connected to arbitrator, autoconnect(0) for client 'Windows 10 lonele', connectState(1).
2024-01-20T08:10:07.553Z In(05) vmx USB: Initializing 'Virtual HID' backend
2024-01-20T08:10:07.553Z In(05) sensorThread VTHREAD 8208 "sensorThread"
2024-01-20T08:10:07.553Z In(05) vmx USB: Initializing 'Remote Device' backend
2024-01-20T08:10:07.553Z In(05) vmx RemoteUSBVMX: Retrieved hostId [40 1e 70 61 c7 fe d5 11-80 68 48 5b 39 ca a5 a7]
2024-01-20T08:10:07.553Z In(05) vmx RemoteUSBVMX: Protocol version min:15 current:19
2024-01-20T08:10:07.553Z In(05) vmx RemoteUSBVMX: no delay setting is TRUE.
2024-01-20T08:10:07.553Z In(05) vmx USB: Initializing 'Virtual Mass Storage' backend
2024-01-20T08:10:07.553Z In(05) vmx USB: Initializing 'Virtual RNG' backend
2024-01-20T08:10:07.553Z In(05) vmx USB: Initializing 'Virtual CCID' backend
2024-01-20T08:10:07.555Z In(05) vmx USB-CCID: Could not establish context: SCARD_E_NO_SERVICE(0x8010001d).
2024-01-20T08:10:07.555Z In(05) vmx USB-CCID: Could not establish context: SCARD_E_NO_SERVICE(0x8010001d).
2024-01-20T08:10:07.559Z In(05) usbCCIDEnumCards VTHREAD 9960 "usbCCIDEnumCards"
2024-01-20T08:10:07.559Z In(05) usbCCIDEnumCards USB-CCID: Card enum thread created.
2024-01-20T08:10:07.559Z In(05) vmx USB: Initializing 'Virtual Bluetooth' backend
2024-01-20T08:10:07.559Z In(05) vmx USB: Initializing 'Virtual Audio' backend
2024-01-20T08:10:07.559Z In(05) vmx USB: Initializing 'Virtual Video' backend
2024-01-20T08:10:07.636Z In(05) vmx USBGW: Skipping disk backing for file (F:\vm_lonele\Windows 7 的克隆.vmx).
2024-01-20T08:10:07.637Z In(05) vmx USBGW: Skipping disk backing for file (F:\vm_lonele\Windows 7-cl1-000002.vmdk).
2024-01-20T08:10:07.638Z In(05) vmx USBGW: Skipping disk backing for file (F:\vm_lonele\Windows 7-cl1-000001.vmdk).
2024-01-20T08:10:07.638Z In(05) vmx USBGW: Skipping disk backing for file (F:\vm_lonele\Windows 7-cl1.vmdk).
2024-01-20T08:10:07.639Z In(05) vmx USBGW: Skipping disk backing for file (F:\vm_lonele\Windows 7-cl1.vmdk).
2024-01-20T08:10:07.640Z In(05) vmx USBGW: Skipping disk backing for file (F:\vm_lonele\Windows 7 的克隆-Snapshot1.vmem).
2024-01-20T08:10:07.640Z In(05) vmx USBGW: Skipping disk backing for file (F:\vm_lonele\Windows 7 的克隆-Snapshot1.vmsn).
2024-01-20T08:10:07.641Z In(05) vmx USBGW: Skipping disk backing for file (F:\vm_lonele\Windows 7-cl1-000001.vmdk).
2024-01-20T08:10:07.641Z In(05) vmx USBGW: Skipping disk backing for file (F:\vm_lonele\Windows 7 的克隆-Snapshot2.vmem).
2024-01-20T08:10:07.642Z In(05) vmx USBGW: Skipping disk backing for file (F:\vm_lonele\Windows 7 的克隆-Snapshot2.vmsn).
2024-01-20T08:10:07.643Z In(05) usbCCIDEnumCards USB-CCID: Could not establish context: SCARD_E_NO_SERVICE(0x8010001d).
2024-01-20T08:10:07.657Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "-1"
2024-01-20T08:10:07.657Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "-1"
2024-01-20T08:10:07.657Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "-1"
2024-01-20T08:10:07.657Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "-1"
2024-01-20T08:10:07.657Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "21"
2024-01-20T08:10:07.657Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "22"
2024-01-20T08:10:07.657Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "23"
2024-01-20T08:10:07.657Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "24"
2024-01-20T08:10:07.717Z In(05) vmx SCSI: scsi0: intr coalescing: on period=50msec cifTh=4 iopsTh=2000 hlt=0
2024-01-20T08:10:07.717Z In(05) vmx SCSI0: UNTAGGED commands will be converted to ORDER tags.
2024-01-20T08:10:07.717Z In(05) vmx SCSI DEVICE (scsi0:0): Computed value of scsi0:0.useBounceBuffers: default
2024-01-20T08:10:07.717Z In(05) vmx DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:10:07.717Z In(05) vmx DISKUTIL: scsi0:0 : geometry=13054/255/63
2024-01-20T08:10:07.718Z In(05) vmx SVGA-GFB: Config settings: autodetect=1, numDisplays=1, maxWidth=2560, maxHeight=1600
2024-01-20T08:10:07.718Z In(05) vmx SVGA-GFB: Desired maximum display topology: wh(6688, 5016)
2024-01-20T08:10:07.718Z In(05) vmx SVGA-GFB: Autodetected target gfbSize = 268435456
2024-01-20T08:10:07.718Z In(05) vmx SVGA-GFB: Using Initial       gfbSize = 8388608
2024-01-20T08:10:07.718Z In(05) vmx SVGA-GFB: MaxPrimaryMem      register = 268435456
2024-01-20T08:10:07.718Z In(05) vmx SVGA-GFB: Truncated maximum resolution for register modes to VRAM size: VRAM=8388608 bytes, max wh(1672, 1254)
2024-01-20T08:10:07.718Z In(05) vmx SVGA-GFB: Max wh(1672, 1254), number of displays: 10
2024-01-20T08:10:07.718Z In(05) vmx SVGA-GFB: Allocated gfbSize=8388608
2024-01-20T08:10:07.718Z No(00) vmx ConfigDB: Setting vmotion.checkpointFBSize = "8388608"
2024-01-20T08:10:07.718Z No(00) vmx ConfigDB: Setting vmotion.checkpointSVGAPrimarySize = "268435456"
2024-01-20T08:10:07.718Z In(05) vmx SVGA: SVGA DeviceLabel: svga2
2024-01-20T08:10:07.718Z In(05) vmx SVGACaps: Device       caps : 0xfdffc3e2
2024-01-20T08:10:07.719Z In(05) vmx SVGACaps: Device       caps2: 0x00006fff
2024-01-20T08:10:07.719Z No(00) vmx ConfigDB: Setting vmotion.svga.mobMaxSize = "1073741824"
2024-01-20T08:10:07.719Z No(00) vmx ConfigDB: Setting vmotion.svga.graphicsMemoryKB = "8388608"
2024-01-20T08:10:07.719Z In(05) vmx SVGA: mobMaxSize=1073741824
2024-01-20T08:10:07.719Z In(05) vmx SVGA: graphicsMemoryKB=8388608
2024-01-20T08:10:07.719Z In(05) vmx SVGA: FIFO capabilities 0x0000077f
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host id: 0
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.supports3D bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.baseCapsLevel num 9
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.maxPointSize num 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.maxTextureSize num 16384
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.maxVolumeExtent num 2048
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.maxTextureAnisotropy num 16
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.lineStipple bool 0
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.dxMaxConstantBuffers num 14
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.dxProvokingVertex bool 0
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.sm41 bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.multisample2x bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.multisample4x bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.msFullQuality bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.logicOps bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.bc67 num 9
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.sm5 bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.multisample8x bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.logicBlendOps bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.maxForcedSampleCount num 0
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature host svga.gl43 bool 0
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost id: 0
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.supports3D bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.baseCapsLevel num 9
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.maxPointSize num 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.maxTextureSize num 16384
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.maxVolumeExtent num 2048
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.maxTextureAnisotropy num 16
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.lineStipple bool 0
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.dxMaxConstantBuffers num 14
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.dxProvokingVertex bool 0
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.sm41 bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.multisample2x bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.multisample4x bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.msFullQuality bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.logicOps bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.bc67 num 9
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.sm5 bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.multisample8x bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.logicBlendOps bool 1
2024-01-20T08:10:07.720Z In(05) vmx SVGAFeature evcHost svga.maxForcedSampleCount num 0
2024-01-20T08:10:07.721Z In(05) vmx SVGAFeature evcHost svga.gl43 bool 0
2024-01-20T08:10:07.721Z In(05) vmx SVGA3dClamp: Renderer Provides     BC67Level:     9 (    9,     9)
2024-01-20T08:10:07.721Z In(05) vmx SVGA3dClamp: Renderer Provides BaseCapsLevel:     9 (    9,     9)
2024-01-20T08:10:07.721Z In(05) vmx SVGA3dClamp: Renderer Provides    ClampLevel:     0 (    0,     9)
2024-01-20T08:10:07.721Z In(05) vmx SVGA3dCaps: host, at power on (3d enabled)
2024-01-20T08:10:07.721Z In(05) vmx   cap[  0]: 0x00000001 (3D)
2024-01-20T08:10:07.721Z In(05) vmx   cap[  1]: 0x00000008 (MAX_LIGHTS)
2024-01-20T08:10:07.721Z In(05) vmx   cap[  2]: 0x00000008 (MAX_TEXTURES)
2024-01-20T08:10:07.721Z In(05) vmx   cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2024-01-20T08:10:07.721Z In(05) vmx   cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2024-01-20T08:10:07.721Z In(05) vmx   cap[  5]: 0x00000001 (VERTEX_SHADER)
2024-01-20T08:10:07.721Z In(05) vmx   cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2024-01-20T08:10:07.721Z In(05) vmx   cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2024-01-20T08:10:07.721Z In(05) vmx   cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2024-01-20T08:10:07.721Z In(05) vmx   cap[  9]: 0x00000001 (S23E8_TEXTURES)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 15]: 0x00000001 (QUERY_TYPES)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 17]:   1.000000 (MAX_POINT_SIZE)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2024-01-20T08:10:07.721Z In(05) vmx   cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 62]: 0x00000000 (MISSING62)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 70]: 0x00000000 (DEAD4)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 71]: 0x00000000 (DEAD5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 72]: 0x00000000 (DEAD7)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 73]: 0x00000000 (DEAD6)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 76]: 0x00000000 (DEAD10)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 84]: 0x00000000 (DEAD1)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 85]: 0x00000000 (DEAD8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 86]: 0x00000000 (DEAD9)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 87]: 0x00000001 (LINE_AA)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 88]: 0x00000000 (LINE_STIPPLE)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 92]: 0x00000000 (DEAD3)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 94]: 0x00000000 (DEAD2)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 95]: 0x00000001 (DXCONTEXT)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 96]: 0x00000000 (DEAD11)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 98]: 0x0000000e (DX_MAX_CONSTANT_BUFFERS)
2024-01-20T08:10:07.722Z In(05) vmx   cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2024-01-20T08:10:07.722Z In(05) vmx   cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2024-01-20T08:10:07.722Z In(05) vmx   cap[106]: 0x00000009 (DXFMT_Z_D32)
2024-01-20T08:10:07.722Z In(05) vmx   cap[107]: 0x0000026b (DXFMT_Z_D16)
2024-01-20T08:10:07.722Z In(05) vmx   cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2024-01-20T08:10:07.722Z In(05) vmx   cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2024-01-20T08:10:07.722Z In(05) vmx   cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2024-01-20T08:10:07.722Z In(05) vmx   cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[114]: 0x00000063 (DXFMT_DXT1)
2024-01-20T08:10:07.722Z In(05) vmx   cap[115]: 0x00000063 (DXFMT_DXT2)
2024-01-20T08:10:07.722Z In(05) vmx   cap[116]: 0x00000063 (DXFMT_DXT3)
2024-01-20T08:10:07.722Z In(05) vmx   cap[117]: 0x00000063 (DXFMT_DXT4)
2024-01-20T08:10:07.722Z In(05) vmx   cap[118]: 0x00000063 (DXFMT_DXT5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2024-01-20T08:10:07.722Z In(05) vmx   cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2024-01-20T08:10:07.722Z In(05) vmx   cap[126]: 0x000000e3 (DXFMT_V8U8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[128]: 0x00000063 (DXFMT_CxV8U8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2024-01-20T08:10:07.722Z In(05) vmx   cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2024-01-20T08:10:07.722Z In(05) vmx   cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[136]: 0x00000001 (DXFMT_BUFFER)
2024-01-20T08:10:07.722Z In(05) vmx   cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2024-01-20T08:10:07.722Z In(05) vmx   cap[138]: 0x000001e3 (DXFMT_V16U16)
2024-01-20T08:10:07.722Z In(05) vmx   cap[139]: 0x000003f7 (DXFMT_G16R16)
2024-01-20T08:10:07.722Z In(05) vmx   cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2024-01-20T08:10:07.723Z In(05) vmx   cap[141]: 0x00000001 (DXFMT_UYVY)
2024-01-20T08:10:07.723Z In(05) vmx   cap[142]: 0x00000041 (DXFMT_YUY2)
2024-01-20T08:10:07.723Z In(05) vmx   cap[143]: 0x00000041 (DXFMT_NV12)
2024-01-20T08:10:07.723Z In(05) vmx   cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2024-01-20T08:10:07.723Z In(05) vmx   cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2024-01-20T08:10:07.723Z In(05) vmx   cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2024-01-20T08:10:07.723Z In(05) vmx   cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2024-01-20T08:10:07.723Z In(05) vmx   cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[196]: 0x00000001 (DXFMT_P8)
2024-01-20T08:10:07.723Z In(05) vmx   cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2024-01-20T08:10:07.723Z In(05) vmx   cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2024-01-20T08:10:07.723Z In(05) vmx   cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2024-01-20T08:10:07.723Z In(05) vmx   cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2024-01-20T08:10:07.723Z In(05) vmx   cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[207]: 0x00000063 (DXFMT_ATI1)
2024-01-20T08:10:07.723Z In(05) vmx   cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[210]: 0x00000063 (DXFMT_ATI2)
2024-01-20T08:10:07.723Z In(05) vmx   cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2024-01-20T08:10:07.723Z In(05) vmx   cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2024-01-20T08:10:07.723Z In(05) vmx   cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2024-01-20T08:10:07.723Z In(05) vmx   cap[217]: 0x0000006b (DXFMT_Z_DF16)
2024-01-20T08:10:07.723Z In(05) vmx   cap[218]: 0x0000006b (DXFMT_Z_DF24)
2024-01-20T08:10:07.723Z In(05) vmx   cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[220]: 0x00000001 (DXFMT_YV12)
2024-01-20T08:10:07.723Z In(05) vmx   cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2024-01-20T08:10:07.723Z In(05) vmx   cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2024-01-20T08:10:07.723Z In(05) vmx   cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2024-01-20T08:10:07.724Z In(05) vmx   cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2024-01-20T08:10:07.724Z In(05) vmx   cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2024-01-20T08:10:07.724Z In(05) vmx   cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[244]: 0x00000001 (SM41)
2024-01-20T08:10:07.724Z In(05) vmx   cap[245]: 0x00000001 (MULTISAMPLE_2X)
2024-01-20T08:10:07.724Z In(05) vmx   cap[246]: 0x00000001 (MULTISAMPLE_4X)
2024-01-20T08:10:07.724Z In(05) vmx   cap[247]: 0x00000001 (MS_FULL_QUALITY)
2024-01-20T08:10:07.724Z In(05) vmx   cap[248]: 0x00000001 (LOGICOPS)
2024-01-20T08:10:07.724Z In(05) vmx   cap[249]: 0x00000001 (LOGIC_BLENDOPS)
2024-01-20T08:10:07.724Z In(05) vmx   cap[250]: 0x00000000 (DEAD12)
2024-01-20T08:10:07.724Z In(05) vmx   cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2024-01-20T08:10:07.724Z In(05) vmx   cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2024-01-20T08:10:07.724Z In(05) vmx   cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2024-01-20T08:10:07.724Z In(05) vmx   cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2024-01-20T08:10:07.724Z In(05) vmx   cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2024-01-20T08:10:07.724Z In(05) vmx   cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2024-01-20T08:10:07.724Z In(05) vmx   cap[257]: 0x00000000 (DEAD13)
2024-01-20T08:10:07.724Z In(05) vmx   cap[258]: 0x00000001 (SM5)
2024-01-20T08:10:07.724Z In(05) vmx   cap[259]: 0x00000001 (MULTISAMPLE_8X)
2024-01-20T08:10:07.724Z In(05) vmx   cap[260]: 0x00000000 (MAX_FORCED_SAMPLE_COUNT)
2024-01-20T08:10:07.724Z In(05) vmx   cap[261]: 0x00000000 (GL43)
2024-01-20T08:10:07.724Z In(05) vmx SVGA3dClamp:     Host Provides     BC67Level:     9 (    9,     9)
2024-01-20T08:10:07.724Z In(05) vmx SVGA3dClamp:     Host Provides BaseCapsLevel:     9 (    9,     9)
2024-01-20T08:10:07.724Z In(05) vmx SVGA3dClamp:     Host Provides    ClampLevel:     0 (    0,     9)
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.supports3D = "1"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.baseCapsLevel = "9"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.maxPointSize = "1"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.maxTextureSize = "16384"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.maxVolumeExtent = "2048"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.maxTextureAnisotropy = "16"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.lineStipple = "0"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.dxMaxConstantBuffers = "14"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.dxProvokingVertex = "0"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.sm41 = "1"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.multisample2x = "1"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.multisample4x = "1"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.msFullQuality = "1"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.logicOps = "1"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.bc67 = "9"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.sm5 = "1"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.multisample8x = "1"
2024-01-20T08:10:07.724Z No(00) vmx ConfigDB: Setting vmotion.svga.logicBlendOps = "1"
2024-01-20T08:10:07.724Z In(05) vmx SVGA3dCaps: guest, compatibility level: 9
2024-01-20T08:10:07.724Z In(05) vmx   cap[  0]: 0x00000001 (3D)
2024-01-20T08:10:07.724Z In(05) vmx   cap[  1]: 0x00000008 (MAX_LIGHTS)
2024-01-20T08:10:07.724Z In(05) vmx   cap[  2]: 0x00000008 (MAX_TEXTURES)
2024-01-20T08:10:07.724Z In(05) vmx   cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2024-01-20T08:10:07.725Z In(05) vmx   cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2024-01-20T08:10:07.725Z In(05) vmx   cap[  5]: 0x00000001 (VERTEX_SHADER)
2024-01-20T08:10:07.725Z In(05) vmx   cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2024-01-20T08:10:07.725Z In(05) vmx   cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2024-01-20T08:10:07.725Z In(05) vmx   cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2024-01-20T08:10:07.725Z In(05) vmx   cap[  9]: 0x00000001 (S23E8_TEXTURES)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 15]: 0x00000001 (QUERY_TYPES)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 17]:   1.000000 (MAX_POINT_SIZE)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 62]: 0x00000000 (MISSING62)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 70]: 0x00000000 (DEAD4)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 71]: 0x00000000 (DEAD5)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 72]: 0x00000000 (DEAD7)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 73]: 0x00000000 (DEAD6)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 76]: 0x00000000 (DEAD10)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 84]: 0x00000000 (DEAD1)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 85]: 0x00000000 (DEAD8)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 86]: 0x00000000 (DEAD9)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 87]: 0x00000001 (LINE_AA)
2024-01-20T08:10:07.725Z In(05) vmx   cap[ 88]: 0x00000000 (LINE_STIPPLE)
2024-01-20T08:10:07.726Z In(05) vmx   cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2024-01-20T08:10:07.726Z In(05) vmx   cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2024-01-20T08:10:07.726Z In(05) vmx   cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2024-01-20T08:10:07.726Z In(05) vmx   cap[ 92]: 0x00000000 (DEAD3)
2024-01-20T08:10:07.726Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2024-01-20T08:10:07.726Z In(05) vmx   cap[ 94]: 0x00000000 (DEAD2)
2024-01-20T08:10:07.726Z In(05) vmx   cap[ 95]: 0x00000001 (DXCONTEXT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[ 96]: 0x00000000 (DEAD11)
2024-01-20T08:10:07.726Z In(05) vmx   cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2024-01-20T08:10:07.726Z In(05) vmx   cap[ 98]: 0x0000000e (DX_MAX_CONSTANT_BUFFERS)
2024-01-20T08:10:07.726Z In(05) vmx   cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2024-01-20T08:10:07.726Z In(05) vmx   cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2024-01-20T08:10:07.726Z In(05) vmx   cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2024-01-20T08:10:07.726Z In(05) vmx   cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2024-01-20T08:10:07.726Z In(05) vmx   cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2024-01-20T08:10:07.726Z In(05) vmx   cap[106]: 0x00000009 (DXFMT_Z_D32)
2024-01-20T08:10:07.726Z In(05) vmx   cap[107]: 0x0000026b (DXFMT_Z_D16)
2024-01-20T08:10:07.726Z In(05) vmx   cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2024-01-20T08:10:07.726Z In(05) vmx   cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2024-01-20T08:10:07.726Z In(05) vmx   cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2024-01-20T08:10:07.726Z In(05) vmx   cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[114]: 0x00000063 (DXFMT_DXT1)
2024-01-20T08:10:07.726Z In(05) vmx   cap[115]: 0x00000063 (DXFMT_DXT2)
2024-01-20T08:10:07.726Z In(05) vmx   cap[116]: 0x00000063 (DXFMT_DXT3)
2024-01-20T08:10:07.726Z In(05) vmx   cap[117]: 0x00000063 (DXFMT_DXT4)
2024-01-20T08:10:07.726Z In(05) vmx   cap[118]: 0x00000063 (DXFMT_DXT5)
2024-01-20T08:10:07.726Z In(05) vmx   cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2024-01-20T08:10:07.726Z In(05) vmx   cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2024-01-20T08:10:07.726Z In(05) vmx   cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2024-01-20T08:10:07.726Z In(05) vmx   cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2024-01-20T08:10:07.726Z In(05) vmx   cap[126]: 0x000000e3 (DXFMT_V8U8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[128]: 0x00000063 (DXFMT_CxV8U8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2024-01-20T08:10:07.726Z In(05) vmx   cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2024-01-20T08:10:07.726Z In(05) vmx   cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2024-01-20T08:10:07.726Z In(05) vmx   cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[136]: 0x00000001 (DXFMT_BUFFER)
2024-01-20T08:10:07.726Z In(05) vmx   cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2024-01-20T08:10:07.726Z In(05) vmx   cap[138]: 0x000001e3 (DXFMT_V16U16)
2024-01-20T08:10:07.726Z In(05) vmx   cap[139]: 0x000003f7 (DXFMT_G16R16)
2024-01-20T08:10:07.726Z In(05) vmx   cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2024-01-20T08:10:07.726Z In(05) vmx   cap[141]: 0x00000001 (DXFMT_UYVY)
2024-01-20T08:10:07.726Z In(05) vmx   cap[142]: 0x00000041 (DXFMT_YUY2)
2024-01-20T08:10:07.726Z In(05) vmx   cap[143]: 0x00000041 (DXFMT_NV12)
2024-01-20T08:10:07.726Z In(05) vmx   cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2024-01-20T08:10:07.726Z In(05) vmx   cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2024-01-20T08:10:07.726Z In(05) vmx   cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2024-01-20T08:10:07.726Z In(05) vmx   cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2024-01-20T08:10:07.726Z In(05) vmx   cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2024-01-20T08:10:07.726Z In(05) vmx   cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2024-01-20T08:10:07.726Z In(05) vmx   cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2024-01-20T08:10:07.726Z In(05) vmx   cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2024-01-20T08:10:07.726Z In(05) vmx   cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2024-01-20T08:10:07.726Z In(05) vmx   cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2024-01-20T08:10:07.726Z In(05) vmx   cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2024-01-20T08:10:07.726Z In(05) vmx   cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2024-01-20T08:10:07.726Z In(05) vmx   cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2024-01-20T08:10:07.726Z In(05) vmx   cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2024-01-20T08:10:07.726Z In(05) vmx   cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2024-01-20T08:10:07.727Z In(05) vmx   cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[196]: 0x00000001 (DXFMT_P8)
2024-01-20T08:10:07.727Z In(05) vmx   cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2024-01-20T08:10:07.727Z In(05) vmx   cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2024-01-20T08:10:07.727Z In(05) vmx   cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2024-01-20T08:10:07.727Z In(05) vmx   cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2024-01-20T08:10:07.727Z In(05) vmx   cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[207]: 0x00000063 (DXFMT_ATI1)
2024-01-20T08:10:07.727Z In(05) vmx   cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[210]: 0x00000063 (DXFMT_ATI2)
2024-01-20T08:10:07.727Z In(05) vmx   cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2024-01-20T08:10:07.727Z In(05) vmx   cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2024-01-20T08:10:07.727Z In(05) vmx   cap[217]: 0x0000006b (DXFMT_Z_DF16)
2024-01-20T08:10:07.727Z In(05) vmx   cap[218]: 0x0000006b (DXFMT_Z_DF24)
2024-01-20T08:10:07.727Z In(05) vmx   cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[220]: 0x00000001 (DXFMT_YV12)
2024-01-20T08:10:07.727Z In(05) vmx   cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2024-01-20T08:10:07.727Z In(05) vmx   cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[244]: 0x00000001 (SM41)
2024-01-20T08:10:07.727Z In(05) vmx   cap[245]: 0x00000001 (MULTISAMPLE_2X)
2024-01-20T08:10:07.727Z In(05) vmx   cap[246]: 0x00000001 (MULTISAMPLE_4X)
2024-01-20T08:10:07.727Z In(05) vmx   cap[247]: 0x00000001 (MS_FULL_QUALITY)
2024-01-20T08:10:07.727Z In(05) vmx   cap[248]: 0x00000001 (LOGICOPS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[249]: 0x00000001 (LOGIC_BLENDOPS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[250]: 0x00000000 (DEAD12)
2024-01-20T08:10:07.727Z In(05) vmx   cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2024-01-20T08:10:07.727Z In(05) vmx   cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2024-01-20T08:10:07.727Z In(05) vmx   cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2024-01-20T08:10:07.727Z In(05) vmx   cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2024-01-20T08:10:07.727Z In(05) vmx   cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2024-01-20T08:10:07.727Z In(05) vmx   cap[257]: 0x00000000 (DEAD13)
2024-01-20T08:10:07.727Z In(05) vmx   cap[258]: 0x00000001 (SM5)
2024-01-20T08:10:07.727Z In(05) vmx   cap[259]: 0x00000001 (MULTISAMPLE_8X)
2024-01-20T08:10:07.728Z In(05) vmx SVGA3dClamp:    Guest Requires     BC67Level:     9 (    9,     9)
2024-01-20T08:10:07.728Z In(05) vmx SVGA3dClamp:    Guest Requires BaseCapsLevel:     9 (    9,     9)
2024-01-20T08:10:07.728Z In(05) vmx SVGA3dClamp:    Guest Requires    ClampLevel:     9 (    0,     9)
2024-01-20T08:10:07.731Z In(05) vmx USB: Initializing 'UHCI' host controller
2024-01-20T08:10:07.731Z No(00) vmx ConfigDB: Setting usb:0.present = "TRUE"
2024-01-20T08:10:07.731Z No(00) vmx ConfigDB: Setting usb:0.deviceType = "hid"
2024-01-20T08:10:07.731Z No(00) vmx ConfigDB: Setting usb:0.port = "0"
2024-01-20T08:10:07.731Z No(00) vmx ConfigDB: Setting usb:0.parent = "-1"
2024-01-20T08:10:07.747Z No(00) vmx ConfigDB: Setting usb:1.speed = "2"
2024-01-20T08:10:07.749Z In(05) vmx Ethernet0 MAC Address: 00:0c:29:e3:cd:10
2024-01-20T08:10:07.750Z In(05) vmx USB: Initializing 'EHCI' host controller
2024-01-20T08:10:07.752Z No(00) vmx ConfigDB: Setting vmci0.id = "-37635189"
2024-01-20T08:10:07.767Z In(05) vmx AHCI:Successfully created adapter 'sata0' as num 0
2024-01-20T08:10:07.767Z In(05) vmx SCSI DEVICE (sata0:1): Computed value of sata0:1.useBounceBuffers: default
2024-01-20T08:10:07.767Z In(05) vmx DISKUTIL: sata0:1 : capacity=0 logical sector size=2048
2024-01-20T08:10:07.767Z In(05) vmx DISKUTIL: sata0:1 : geometry=0/0/0
2024-01-20T08:10:07.767Z In(05) vmx AHCI:Creating ATAPI CDROM on SATA adapter.
2024-01-20T08:10:07.767Z In(05) vmx AHCI:Successfully created device: sata0:1
2024-01-20T08:10:07.773Z In(05) vmx WORKER: Creating new group with maxThreads=1 (34)
2024-01-20T08:10:07.773Z In(05) vmx DISKUTIL: scsi0:0 : max toolsVersion = 11365, type = 1
2024-01-20T08:10:07.773Z In(05) vmx TOOLS setting legacy tools version to '11365' type 1, manifest status is 9
2024-01-20T08:10:07.774Z In(05) vmx Tools: sending 'OS_PowerOn' (state = 3) state change request
2024-01-20T08:10:07.774Z In(05) vmx Tools: Delaying state change request to state 3.
2024-01-20T08:10:07.774Z In(05) vmx TOOLS INSTALL initializing state to IDLE on power on.
2024-01-20T08:10:07.774Z In(05) vmx TOOLS INSTALL updating Rpc handlers registration.
2024-01-20T08:10:07.774Z In(05) vmx TOOLS INSTALL register RPC: upgrader.setGuestFileRoot
2024-01-20T08:10:07.774Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.is_image_inserted
2024-01-20T08:10:07.774Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.installerActive
2024-01-20T08:10:07.774Z In(05) vmx TOOLS INSTALL register RPC: guest.upgrader_send_cmd_line_args
2024-01-20T08:10:07.774Z In(05) worker-5020 ToolsISO: Refreshing imageName for 'windows7' (refreshCount=1, lastCount=1).
2024-01-20T08:10:07.774Z In(05) worker-5020 ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-01-20T08:10:07.774Z In(05) worker-5020 ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-01-20T08:10:07.775Z In(05) vmx P9FS_PowerOn: 9PFS server is not enabled.
2024-01-20T08:10:07.775Z In(05) vmx HgfsServerManagerVigorInit: Initialize: dev api
2024-01-20T08:10:07.775Z In(05) vmx HgfsServer_InitState: initialized notification inactive.
2024-01-20T08:10:07.775Z In(05) worker-5020 ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-01-20T08:10:07.775Z In(05) worker-5020 ToolsISO: Selected Tools ISO 'windows.iso' for 'windows7' guest.
2024-01-20T08:10:07.775Z In(05) worker-5020 GetHostManifests: Extracting C:\Users\<USER>\AppData\Local\Temp\vmware-bfdemo\manifest.txt.5020.iso.shipped manifest file.
2024-01-20T08:10:07.775Z In(05) hgfsOplockThread VTHREAD 8300 "hgfsOplockThread"
2024-01-20T08:10:07.775Z In(05) vmx HgfsServer_InitState: initialized threadpool active.
2024-01-20T08:10:07.775Z In(05) worker-5020 ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\windows.iso.sha failed: Could not find the file
2024-01-20T08:10:07.775Z In(05) worker-5020 ToolsISO: Unable to read hash file C:\Program Files (x86)\VMware\VMware Workstation\windows.iso.sha, ignoring.
2024-01-20T08:10:07.775Z In(05) vmx HgfsChannelActivateChannel: HGFS [backdoor] channel ACTIVATED.
2024-01-20T08:10:07.776Z In(05) vmx HgfsChannelActivateChannel: HGFS [vmci] channel ACTIVATED.
2024-01-20T08:10:07.776Z In(05) vmx MKSVMX: Copy/paste enabled = 1
2024-01-20T08:10:07.776Z In(05) vmx DEPLOYPKG: No pending deploy package name set
2024-01-20T08:10:07.776Z In(05) vmx DEPLOYPKG: ToolsDeployPkgPublishState: state=0, code=0, message=(null)
2024-01-20T08:10:07.777Z In(05) worker-5020 GetHostManifests: Done extracting the manifest file.
2024-01-20T08:10:07.777Z In(05) worker-5020 Win32U_GetFileAttributes: GetFileAttributesExW("C:\Program Files (x86)\VMware\VMware Workstation\windows_avr_manifest.txt", ...) failed, error: 2
2024-01-20T08:10:07.778Z In(05) vmx MonPmc: ctrBase 0xc0010004 selBase 0xc0010000/1 PGC 0/0 SMM 0 drain 0 AMD 1
2024-01-20T08:10:07.778Z In(05)+ vmx MonPmc:   gen counters num: 4 width 48 write width 48
2024-01-20T08:10:07.778Z In(05)+ vmx MonPmc:   fix counters num: 0 width 0; version 0
2024-01-20T08:10:07.778Z In(05)+ vmx MonPmc:   unavailable counters: 0
2024-01-20T08:10:07.778Z In(05) worker-5020 Using ToolsMinVersion = 8384
2024-01-20T08:10:07.778Z In(05) worker-5020 ToolsVersionGetStatusWorkerThread: Tools status 3 derived from environment
2024-01-20T08:10:07.781Z No(00) vmx ConfigDB: Setting monitor.phys_bits_used = "45"
2024-01-20T08:10:07.781Z In(05) vmx Full guest CPUID with differences from hostCPUID highlighted.
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest vendor: AuthenticAMD
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest family: 0x10 model: 0x4 stepping: 0x3
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest codename: Shanghai (K10)
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest name: AMD Phenom(tm) II X4 945 Processor
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID       level eaxIn, ecxIn:        eax        ebx        ecx        edx
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 00000000,  0: 0x00000005 0x68747541 0x444d4163 0x69746e65
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 00000001,  0: 0x00100f43 0x00000800 0x80802001 0x078bfbff
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID *host level 00000001,  0: 0x00100f43 0x00040800 0x00802009 0x178bfbff
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 40000000,  0: 0x40000010 0x61774d56 0x4d566572 0x65726177
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 40000001,  0: 0x31237648 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 40000003,  0: 0x00000a72 0x00000000 0x00000000 0x00000508
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 40000004,  0: 0x00000020 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 40000005,  0: 0xffffffff 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 40000010,  0: 0x002dedff 0x000101d0 0x00000001 0x00000000
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 80000000,  0: 0x8000001b 0x68747541 0x444d4163 0x69746e65
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 80000001,  0: 0x00100f43 0x10001ad6 0x000003fd 0xefd3fbff
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID *host level 80000001,  0: 0x00100f43 0x10001ad6 0x000037ff 0xefd3fbff
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 80000002,  0: 0x20444d41 0x6e656850 0x74286d6f 0x4920296d
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 80000003,  0: 0x34582049 0x35343920 0x6f725020 0x73736563
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 80000004,  0: 0x0000726f 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 80000005,  0: 0xff30ff10 0xff30ff20 0x40020140 0x40020140
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 80000006,  0: 0x20800000 0x42004200 0x02008140 0x0030b140
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 80000007,  0: 0x00000000 0x00000003 0x00000000 0x00000100
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID *host level 80000007,  0: 0x00000000 0x00000000 0x00000000 0x000001f9
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 80000008,  0: 0x0000302d 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID *host level 80000008,  0: 0x00003030 0x00000000 0x00002003 0x00000000
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 8000000a,  0: 0x00000001 0x00000040 0x00000000 0x0000000d
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID *host level 8000000a,  0: 0x00000001 0x00000040 0x00000000 0x0000000f
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 80000019,  0: 0xf0300000 0x60100000 0x00000000 0x00000000
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 8000001a,  0: 0x00000003 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.781Z In(05) vmx guest vs. host CPUID guest level 8000001b,  0: 0x0000001f 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.781Z In(05) vmx Minimum ucode level: 0x00000000
2024-01-20T08:10:07.781Z In(05) vmx VPMC: events will use hybrid freeze.
2024-01-20T08:10:07.781Z In(05) vmx VPMC: gen counters: num 4 mask 0xffffffffffff
2024-01-20T08:10:07.781Z In(05) vmx VPMC: hardware counters: 0
2024-01-20T08:10:07.782Z In(05) vmx SVGA-PCI: BAR gfbSize=134217728, fifoSize=8388608
2024-01-20T08:10:07.782Z In(05) vmx SVGA: SVGA_REG_MEMORY_SIZE=8388608
2024-01-20T08:10:07.782Z In(05) vmx SVGA: SVGA_REG_VRAM_SIZE=8388608
2024-01-20T08:10:07.782Z In(05) vmx BusMemSampleSetUpStats: touched: initPct 75 pages 393216 : dirtied: initPct 75 pages 393216
2024-01-20T08:10:07.782Z In(05) vmx MemSched: caller 0 numvm 1 locked pages: num 1984 max 4120576
2024-01-20T08:10:07.782Z In(05) vmx MemSched: locked Page Limit: host 4491699 config 4128768
2024-01-20T08:10:07.782Z In(05) vmx MemSched: minmempct 50  timestamp 411
2024-01-20T08:10:07.782Z In(05) vmx MemSched: VM 0 min 274967 max 537111 shares 524288 paged 3957055 nonpaged 4755 anonymous 8068 locked 1984 touchedPct 75 dirtiedPct 75 timestamp 411 vmResponsive is 1
2024-01-20T08:10:07.782Z In(05) vmx MemSched: locked 1984 target 537111 balloon 0 0 0 swapped 0 0 allocd 0 512 state 0 100
2024-01-20T08:10:07.782Z In(05) vmx MemSched: states: 0 1 : 1 0 : 2 0 : 3 0
2024-01-20T08:10:07.782Z In(05) vmx MemSched: Balloon enabled 1 guestType 0 maxSize 0
2024-01-20T08:10:07.782Z In(05) vmx PStrIntern expansion: nBkts=256
2024-01-20T08:10:07.782Z In(05) vmx FeatureCompat: Capabilities:
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.sse3 = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.mwait = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.cmpxchg16b = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.popcnt = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.lahf64 = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.svm = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.extapicspc = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.cr8avail = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.abm = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.sse4a = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.misaligned_sse = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.3dnprefetch = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.nx = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.mmxext = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.ffxsr = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.pdpe1gb = 1
2024-01-20T08:10:07.782Z In(05) vmx Capability Found: cpuid.rdtscp = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: cpuid.lm = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: cpuid.3dnowplus = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: cpuid.3dnow = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: cpuid.svm_npt = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: cpuid.svm_nrip = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: cpuid.amd = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: hv.capable = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: misc.cpuidfaulting = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: vpmc.microarchitecture.shanghai = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: vpmc.numgenctrs = 4
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: vpmc.genwidth = 0x30
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: vpmc.genctr.0 = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: vpmc.genctr.1 = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: vpmc.genctr.2 = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: vpmc.genctr.3 = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.supports3d = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.basecapslevel = 9
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.maxpointsize = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.maxtexturesize = 0x4000
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.maxvolumeextent = 0x800
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.maxtextureanisotropy = 0x10
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.dxmaxconstantbuffers = 0xe
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.sm41 = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.multisample2x = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.multisample4x = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.msfullquality = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.logicops = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.bc67 = 9
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.sm5 = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.multisample8x = 1
2024-01-20T08:10:07.783Z In(05) vmx Capability Found: svga0*svga.logicblendops = 1
2024-01-20T08:10:07.783Z In(05) vmx FeatureCompat: Requirements:
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.sse3 - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.cmpxchg16b - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.popcnt - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.lahf64 - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.svm - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.extapicspc - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.cr8avail - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.abm - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.sse4a - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.misaligned_sse - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.3dnprefetch - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.nx - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.mmxext - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.ffxsr - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.pdpe1gb - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.rdtscp - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.lm - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.3dnowplus - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.3dnow - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.svm_npt - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.svm_nrip - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: cpuid.amd - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: hv.capable - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: misc.cpuidfaulting - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.supports3d - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.basecapslevel - Num:Min:9
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.maxpointsize - Num:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.maxtexturesize - Num:Min:0x4000
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.maxvolumeextent - Num:Min:0x800
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.maxtextureanisotropy - Num:Min:0x10
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.dxmaxconstantbuffers - Num:Min:0xe
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.sm41 - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.multisample2x - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.multisample4x - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.msfullquality - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.logicops - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.bc67 - Num:Min:9
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.sm5 - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.multisample8x - Bool:Min:1
2024-01-20T08:10:07.783Z In(05) vmx VM Features Required: svga*svga.logicblendops - Bool:Min:1
2024-01-20T08:10:07.786Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '1'
2024-01-20T08:10:07.787Z In(05) vmx Guest AMD-V Capabilities:
2024-01-20T08:10:07.787Z In(05) vmx SVM Revision:           1
2024-01-20T08:10:07.787Z In(05) vmx ASIDs Supported:        64
2024-01-20T08:10:07.787Z In(05) vmx Nested Paging:          yes
2024-01-20T08:10:07.787Z In(05) vmx SVM GMET:               no
2024-01-20T08:10:07.787Z In(05) vmx LBR Virtualization:     no
2024-01-20T08:10:07.787Z In(05) vmx SVM Lock:               yes
2024-01-20T08:10:07.787Z In(05) vmx NRIP Save:              yes
2024-01-20T08:10:07.787Z In(05) vmx TSC Rate MSR:           no
2024-01-20T08:10:07.787Z In(05) vmx VMCB Clean Bits:        no
2024-01-20T08:10:07.787Z In(05) vmx Flush by ASID:          no
2024-01-20T08:10:07.787Z In(05) vmx Decode Assists:         no
2024-01-20T08:10:07.787Z In(05) vmx Pause Filter:           no
2024-01-20T08:10:07.787Z In(05) vmx Pause Filter Threshold: no
2024-01-20T08:10:07.787Z In(05) vmx 
2024-01-20T08:10:07.787Z In(05)+ vmx OvhdMem: Static (Power On) Overheads
2024-01-20T08:10:07.787Z In(05) vmx                                                       reserved      |          used
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  524288 524288      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7424   7424      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   15360  15360      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem Total excluded                      :  547072 547072      - |      -      -      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem Actual maximum                      :         547072        |             -
2024-01-20T08:10:07.787Z In(05)+ vmx 
2024-01-20T08:10:07.787Z In(05) vmx                                                       reserved      |          used
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       2      2      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       1      1      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  1048576 1048576      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :    2944   2944      - |    385    385      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :     196    196      - |    196    196      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaShaderTable            :    1000   1000      - |    193    193      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaShaderText             :    2304   2304      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaContextCache           :    1357   1357      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaDXRTViewTable          :      31     31      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaDXDSViewTable          :      16     16      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaDXSRViewTable          :    1021   1021      - |    193    193      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaDXUAViewTable          :      70     70      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaDXBlendStateTable      :      31     31      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaDXElementLayoutTable   :     199    199      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaDXDepthStencilTable    :      19     19      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaDXRasterizerStateTable :      19     19      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaDXSamplerTable         :     141    141      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaDXContextTable         :    1472   1472      - |      0      0      -
2024-01-20T08:10:07.787Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderTable          :     241    241      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderText           :    4096   4096      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SvgaDXStreamOutTable       :     516    516      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SvgaDXQueryTable           :     132    132      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SvgaVAProcessorTable       :       1      1      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SvgaVADecoderTable         :       1      1      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :    1026   1026      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :   10752  10752      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :    3494   3494      - |   3492   3492      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  2547712 2547712      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxMksLLVM                 :   12288  12288      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    2560   2560      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   17664  17664      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem Total paged                         :  3957055 3957055      - |   4469   4469      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem Actual maximum                      :         3957055        |          4469
2024-01-20T08:10:07.788Z In(05)+ vmx 
2024-01-20T08:10:07.788Z In(05) vmx                                                       reserved      |          used
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :     103    103      - |     87     87      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      19     19      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_PFrame                     :    1756   1967      - |   1756   1756      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |     16     16      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |     64     64      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      1      1      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      3      3      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       1      1      - |      1      1      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_AHCIBIOS                   :      16     16      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    2048   2048      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      1      1      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      1      1      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       1      1      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       1      1      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      35     35      - |     35     35      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_MonNuma                    :     237    237      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem Total nonpaged                      :    4544   5203      - |   1965   1965      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem Actual maximum                      :           5203        |          1965
2024-01-20T08:10:07.788Z In(05)+ vmx 
2024-01-20T08:10:07.788Z In(05) vmx                                                       reserved      |          used
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_Alloc                       :      97     97      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :     579    588      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :       8      8      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       4      4      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       1      1      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      24     24      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_TC                          :     513    513      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       5      5      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       6      6      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       2      2      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_HV                          :       3      3      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       2      2      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_VHV                         :       3      3      - |      0      0      -
2024-01-20T08:10:07.788Z In(05) vmx OvhdMem OvhdMon_VNPTShadow                  :    1869   1869      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_VNPTShadowCache             :      17     17      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_VNPTBackmap                 :    1033   1179      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_SVMIDT                      :       1      1      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_Numa                        :      24     24      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_NumaTextRodata              :     211    211      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_NumaDataBss                 :      26     26      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      29     29      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    1883   1883      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    1194   1194      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     264    264      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       6      6      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem Total anonymous                     :    7913   8068      - |      0      0      -
2024-01-20T08:10:07.789Z In(05) vmx OvhdMem Actual maximum                      :           8068        |             0
2024-01-20T08:10:07.789Z In(05)+ vmx 
2024-01-20T08:10:07.789Z In(05) vmx VMMEM: Precise Reservation: 15505MB (MainMem=2048MB)
2024-01-20T08:10:07.789Z In(05) vmx VMXSTATS: Registering 4 stats: vmx.overheadMemSize
2024-01-20T08:10:07.789Z In(05) vmx Vix: [mainDispatch.c:1051]: VMAutomation_PowerOn. Powering on.
2024-01-20T08:10:07.791Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 1
2024-01-20T08:10:07.791Z No(00) vmx ConfigDB: Setting cleanShutdown = "FALSE"
2024-01-20T08:10:07.791Z No(00) vmx ConfigDB: Setting softPowerOff = "FALSE"
2024-01-20T08:10:07.816Z In(05) vcpu-0 VTHREAD 952 "vcpu-0"
2024-01-20T08:10:07.817Z In(05) vmx ToolsISO: Refreshing imageName for 'windows7' (refreshCount=1, lastCount=1).
2024-01-20T08:10:07.817Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-01-20T08:10:07.817Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-01-20T08:10:07.818Z In(05) vmx ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-01-20T08:10:07.818Z In(05) vmx ToolsISO: Selected Tools ISO 'windows.iso' for 'windows7' guest.
2024-01-20T08:10:07.818Z In(05) vmx TOOLS updated cached value for isoImageExists to 1.
2024-01-20T08:10:07.818Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'ok', 'current', install possible
2024-01-20T08:10:07.824Z In(05) vcpu-0 MonTimer APIC:0/0 vec: 0
2024-01-20T08:10:07.824Z In(05) vcpu-0 APIC: version = 0x10, max LVT = 5, LDR = 0x1000000, DFR = 0xffffffff
2024-01-20T08:10:07.824Z In(05) vcpu-0 Active HV capabilities
2024-01-20T08:10:07.824Z In(05) vcpu-0    Address space identifiers
2024-01-20T08:10:07.824Z In(05) vcpu-0    MSR permission bitmap
2024-01-20T08:10:07.824Z In(05) vcpu-0    Nested paging A/D bits
2024-01-20T08:10:07.824Z In(05) vcpu-0    Next RIP
2024-01-20T08:10:07.824Z In(05) vcpu-0    Guest physical width
2024-01-20T08:10:07.824Z In(05) vcpu-0    Real-address mode
2024-01-20T08:10:07.828Z In(05) vcpu-0 CPU reset: hard (mode Emulation)
2024-01-20T08:10:07.831Z In(05) vcpu-0 GuestRpc: Successfully created RPCI listening socket.
2024-01-20T08:10:07.831Z In(05) vcpu-0 GuestRpc: Using vsocket for TCLO messaging is disabled.
2024-01-20T08:10:07.831Z No(00) vcpu-0 ConfigDB: Unsetting all entries with prefix "usb:0."
2024-01-20T08:10:07.863Z In(05) vcpu-0 USB: Disconnecting device 0x2000000400000000
2024-01-20T08:10:07.863Z In(05) vcpu-0 USB: Connecting device desc:name:VMware\ Virtual\ USB\ Mouse vid:0e0f pid:0003 speed:full family:hid deviceType:virtual-hid info:0000005 version:4 id:0x200000050e0f0003
2024-01-20T08:10:07.863Z No(00) vcpu-0 ConfigDB: Setting usb:0.present = "TRUE"
2024-01-20T08:10:07.863Z No(00) vcpu-0 ConfigDB: Setting usb:0.deviceType = "hid"
2024-01-20T08:10:07.863Z No(00) vcpu-0 ConfigDB: Setting usb:0.port = "0"
2024-01-20T08:10:07.863Z No(00) vcpu-0 ConfigDB: Setting usb:0.parent = "-1"
2024-01-20T08:10:07.899Z In(05) vcpu-0 memoryHotplug: Node 0: Present: 2047 MB (100 %) Size:3071 MB (100 %)
2024-01-20T08:10:07.899Z In(05) vcpu-0 PIIX4: PM Resuming from suspend type 0x0, chipset.onlineStandby 0
2024-01-20T08:10:07.899Z In(05) vcpu-0 SOUNDLIB: Creating the Wave sound backend.
2024-01-20T08:10:07.899Z In(05) vcpu-0 HDAudio: HDAudioConnectDisconnect: primary backend: wave, fallback backend: dummy.
2024-01-20T08:10:07.899Z In(05) vcpu-0 VNET: 'ethernet0' enable link state propagation, lsp.state = 5
2024-01-20T08:10:07.900Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'ethernet0' lsp.state = 4
2024-01-20T08:10:07.900Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'Ethernet0' notify available.
2024-01-20T08:10:07.903Z In(05) vcpu-0 Msg_Post: Warning
2024-01-20T08:10:07.904Z In(05) vcpu-0 [msg.serial.thinprint.disabled] The virtual printing feature is globally disabled on this system, and will not be enabled for this virtual machine.
2024-01-20T08:10:07.904Z In(05) vcpu-0 [msg.device.startdisconnected] Virtual device 'serial0' will start disconnected.
2024-01-20T08:10:07.904Z In(05) vcpu-0 ----------------------------------------
2024-01-20T08:10:07.991Z In(05) vcpu-0 CDROM: Connecting sata0:1 to 'C:\Users\<USER>\Videos\Win10_22H2_Chinese_Simplified_x32v1.iso'. type=2 remote=0
2024-01-20T08:10:07.995Z In(05) vcpu-0 DMG_Open: Not an unencrypted .dmg file (footer signature 0x00000000).
2024-01-20T08:10:07.995Z In(05) vcpu-0 DMG_Close: max cached entries 8
2024-01-20T08:10:07.995Z In(05) vcpu-0 CDROM: Checking initial physical media state...
2024-01-20T08:10:07.995Z In(05) vcpu-0 CDROM:  initial physical CDROM state is 1 (sense)
2024-01-20T08:10:07.997Z In(05) vcpu-0 HGFSPublish: publishing 6 shares
2024-01-20T08:10:08.002Z In(05) vcpu-0 Win32U_GetFileAttributes: GetFileAttributesExW("F:\vm_lonele\Windows 7 的克隆.vmpl", ...) failed, error: 2
2024-01-20T08:10:08.002Z In(05) vcpu-0 PolicyVMXFindPolicyKey: policy file does not exist.
2024-01-20T08:10:08.012Z In(05) vcpu-0 Vix: [mainDispatch.c:4206]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1872, success=1 additionalError=0
2024-01-20T08:10:08.012Z In(05) vcpu-0 Vix: [mainDispatch.c:4123]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=0, err=0).
2024-01-20T08:10:08.012Z In(05) vcpu-0 Vix: [mainDispatch.c:4123]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2024-01-20T08:10:08.012Z In(05) vcpu-0 Transitioned vmx/execState/val to poweredOn
2024-01-20T08:10:08.012Z In(05) vcpu-0 Tools: Adding Tools inactivity timer.
2024-01-20T08:10:08.012Z In(05) vmx VUsbUpdateVigorFieldsAndAutoconnect: New set of 1 USB devices
2024-01-20T08:10:08.012Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4]
2024-01-20T08:10:08.019Z In(05) vcpu-0 AMD-V enabled.
2024-01-20T08:10:08.020Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-01-20T08:10:08.020Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 4 to 6.
2024-01-20T08:10:08.033Z In(05) vmx USB: Connecting device desc:name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4 id:0x700000010e0f0008
2024-01-20T08:10:08.036Z Wa(03) vmx Bluetooth-Win32: ERROR retrieving local radio info: No more data is available
2024-01-20T08:10:08.036Z Wa(03) vmx Bluetooth host backend not available.
2024-01-20T08:10:08.090Z In(05) mks MKSControlMgr: connected
2024-01-20T08:10:08.130Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2024-01-20T08:10:08.168Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-01-20T08:10:08.173Z In(05) vcpu-0 UHCI: HCReset
2024-01-20T08:10:08.233Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-01-20T08:10:08.243Z In(05) mks DXGIPresentation: Enumerating adapter 0
2024-01-20T08:10:08.243Z In(05) mks DXGIPresentation: `AMD Radeon HD 5700 Series` vendor=0x1002 device=0x68be revision=0
2024-01-20T08:10:08.243Z In(05) mks DXGIPresentation: video=1010MB system=0MB shared=3840MB
2024-01-20T08:10:08.283Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0x0) and 0xfe000000(0x0)
2024-01-20T08:10:08.283Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:08.288Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-01-20T08:10:08.288Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:08.289Z In(05) mks DXGIPresentation: Using device unknown; adapter `AMD Radeon HD 5700 Series`
2024-01-20T08:10:08.289Z In(05) mks DXGIPresentation: Enumerating adapter 1
2024-01-20T08:10:08.289Z In(05) mks DXGIPresentation: `AMD Radeon HD 5700 Series` vendor=0x1002 device=0x68be revision=0
2024-01-20T08:10:08.289Z In(05) mks DXGIPresentation: video=1010MB system=0MB shared=3840MB
2024-01-20T08:10:08.289Z In(05) mks DXGIPresentation: Enumerating adapter 2
2024-01-20T08:10:08.289Z In(05) mks DXGIPresentation: `Microsoft Basic Render Driver` vendor=0x1414 device=0x008c revision=0
2024-01-20T08:10:08.289Z In(05) mks DXGIPresentation: video=0MB system=0MB shared=9215MB
2024-01-20T08:10:08.289Z In(05) mks DXGIPresentation: LOCAL     budget  8498M usage     0M avail  4377M res     0M
2024-01-20T08:10:08.289Z In(05) mks DXGIPresentation: NON-LOCAL budget     0M usage     0M avail     0M res     0M
2024-01-20T08:10:08.299Z In(05) mks MKS-HWinMux: Started DXGI presentation backend.
2024-01-20T08:10:08.299Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2024-01-20T08:10:08.412Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2024-01-20T08:10:08.479Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-01-20T08:10:08.479Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:08.485Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-01-20T08:10:08.485Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:08.485Z In(05) vcpu-0 SVGA: Registering IOSpace at 0x1070
2024-01-20T08:10:08.485Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-01-20T08:10:08.485Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:08.489Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.490Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.490Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.490Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.491Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.491Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.491Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.492Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.492Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.493Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.493Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.493Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.494Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.494Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.494Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.495Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.495Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.496Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.496Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.496Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.497Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.497Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.498Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.498Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.498Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.499Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.499Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.499Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.500Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.500Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.501Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.501Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.523Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:08.534Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:08.537Z In(05) vmx USB: Connecting device desc:name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4 id:0x700000010e0f0008
2024-01-20T08:10:08.537Z Wa(03) vmx Bluetooth-Win32: ERROR retrieving local radio info: No more data is available
2024-01-20T08:10:08.537Z Wa(03) vmx Bluetooth host backend not available.
2024-01-20T08:10:08.564Z In(05) vmx VUsbUpdateVigorFieldsAndAutoconnect: New set of 1 USB devices
2024-01-20T08:10:08.564Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4]
2024-01-20T08:10:08.649Z In(05) vcpu-0 AHCI: Tried to enable/disable IO space.
2024-01-20T08:10:08.650Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.651Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.651Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.651Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.652Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.652Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.653Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.653Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.653Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.654Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.654Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.655Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.655Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.655Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.656Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.656Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.657Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.657Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.657Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.658Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.658Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.658Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.659Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.659Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.660Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.660Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.660Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.661Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.661Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.661Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.662Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.662Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:08.863Z In(05) vcpu-0 AHCI: Tried to enable/disable IO space.
2024-01-20T08:10:08.863Z In(05) vcpu-0 AHCI-VMM:HBA reset issued on sata0.
2024-01-20T08:10:08.873Z In(05) vcpu-0 DISKUTIL: scsi0:0 : geometry=13054/255/63
2024-01-20T08:10:08.873Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:10:09.064Z In(05) vcpu-0 BIOS-UUID is 56 4d 1b fb d1 ef e5 0c-eb 4e 61 52 46 e3 cd 10
2024-01-20T08:10:09.537Z In(05) vmx USB: Connecting device desc:name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4 id:0x700000010e0f0008
2024-01-20T08:10:09.537Z Wa(03) vmx Bluetooth-Win32: ERROR retrieving local radio info: No more data is available
2024-01-20T08:10:09.537Z Wa(03) vmx Bluetooth host backend not available.
2024-01-20T08:10:09.630Z In(05) vcpu-0 DDB: "longContentID" = "7b5e25c41c2c70b9e324251451e8494e" (was "b1dac87064a8b58bf75d5b76c0d2d2e4")
2024-01-20T08:10:09.660Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:09.660Z In(05) svga SVGA enabling SVGA
2024-01-20T08:10:09.665Z In(05) svga SVGA-ScreenMgr: Screen type changed to RegisterMode
2024-01-20T08:10:11.538Z In(05) vmx USB: Connecting device desc:name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4 id:0x700000010e0f0008
2024-01-20T08:10:11.538Z Wa(03) vmx Bluetooth-Win32: ERROR retrieving local radio info: No more data is available
2024-01-20T08:10:11.538Z Wa(03) vmx Bluetooth host backend not available.
2024-01-20T08:10:14.636Z In(05) vcpu-0 SVGA: Unregistering IOSpace at 0x1070
2024-01-20T08:10:14.636Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-01-20T08:10:14.636Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:14.637Z In(05) vcpu-0 SVGA: Registering IOSpace at 0x1070
2024-01-20T08:10:14.637Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-01-20T08:10:14.637Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:14.902Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.903Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.904Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.904Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.905Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.905Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.905Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.906Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.906Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.906Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.906Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.907Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.907Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.907Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.907Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.907Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.908Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.908Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.908Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.908Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.908Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.908Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.909Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.909Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.909Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.909Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.909Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.909Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.909Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.910Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.910Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.910Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.910Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.910Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.910Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.910Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.910Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.911Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.911Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.911Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.911Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.911Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.911Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.911Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.911Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.911Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.912Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.912Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.912Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.912Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.912Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.912Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.912Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.912Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.912Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.912Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.913Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.913Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.913Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.913Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.913Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.913Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.913Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.913Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.913Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.913Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.913Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.914Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.915Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.915Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.915Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.915Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.915Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.915Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.915Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.915Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.915Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.915Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.916Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.916Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.916Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.916Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.916Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.916Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.916Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.916Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.916Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.917Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.917Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.917Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.917Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.917Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.917Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.917Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.917Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.918Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.919Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.919Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.919Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.919Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.919Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.919Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.919Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.919Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.919Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.919Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.919Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.920Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.920Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.920Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.920Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.920Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.920Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.921Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.922Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.923Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.923Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.923Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.923Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.923Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.923Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.923Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.924Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.925Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.926Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.927Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.927Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.927Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.927Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.927Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.927Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.927Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.927Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.928Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.929Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.930Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.930Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.930Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.930Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.930Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.930Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.931Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.932Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.932Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.932Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.932Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.932Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.932Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.932Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.932Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.933Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.934Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.935Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.935Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.935Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.935Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.935Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.935Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.935Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.936Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.937Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.937Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.937Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.937Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.938Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.939Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.939Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.939Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.939Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.939Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.939Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.939Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.939Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.939Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.939Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.939Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.940Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.941Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.942Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.942Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.942Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.942Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.942Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.942Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.942Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.942Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.942Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.942Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.943Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.944Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.944Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.944Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.944Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.945Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.977Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:14.977Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.024Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.024Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.056Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.056Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.087Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.087Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.118Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.118Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.150Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.150Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.181Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.181Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.212Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.212Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.243Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.243Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.275Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.275Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.306Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.306Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.337Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.337Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.368Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.368Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.399Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.399Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.430Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.431Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.462Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.462Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.493Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.493Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.524Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.524Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.556Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.556Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.587Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.587Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.619Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.619Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.650Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.650Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.681Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.681Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.713Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.713Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.744Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.744Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.775Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.775Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.806Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.806Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.838Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.838Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.869Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.869Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.888Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2024-01-20T08:10:15.900Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.900Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.931Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.931Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.962Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:15.962Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.078Z In(05) vcpu-0 Guest: Driver=vsock, Version=9.8.17.0
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.084Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.085Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.085Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.085Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.085Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.085Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.085Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.085Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.085Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.085Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.121Z In(05) vcpu-0 SCSI0: RESET BUS
2024-01-20T08:10:16.123Z In(05) vcpu-0 LSI: Invalid PageType [21] pageNo 0 Action 0
2024-01-20T08:10:16.126Z In(05) vcpu-0 SCSI scsi0:0: Unsupported command REPORT LUNS issued. --ok
2024-01-20T08:10:16.135Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.135Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.135Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.135Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.136Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.137Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.138Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.138Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.138Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.138Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.138Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.138Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.138Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.138Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.206Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.207Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.207Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.207Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.207Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.207Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:16.244Z In(05) vcpu-0 AHCI-VMM:HBA reset issued on sata0.
2024-01-20T08:10:16.478Z In(05) vcpu-0 Tools: Running status rpc handler: 0 => 1.
2024-01-20T08:10:16.478Z In(05) vcpu-0 Tools: Changing running status: 0 => 1.
2024-01-20T08:10:16.478Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 1 (last received 0s ago)
2024-01-20T08:10:16.478Z In(05) vcpu-0 Tools: Removing Tools inactivity timer.
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.095Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.096Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.096Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.096Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:18.145Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:10:18.145Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:18.145Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:18.146Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:10:18.146Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:10:18.147Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x1c
2024-01-20T08:10:18.148Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:10:18.154Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:10:19.898Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:10:21.221Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.221Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.221Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.221Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.221Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.222Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.223Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.223Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.227Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.227Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.227Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.227Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.227Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.227Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.227Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.227Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.227Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.228Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.230Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.231Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.231Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.231Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.231Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.231Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.231Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.231Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.231Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.232Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.233Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.234Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.235Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.235Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.235Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.235Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.235Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.235Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.235Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.235Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.235Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.236Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.237Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.238Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.238Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-01-20T08:10:21.249Z In(05) vcpu-0 SVGA: Unregistering IOSpace at 0x1070
2024-01-20T08:10:21.249Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-01-20T08:10:21.250Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:21.250Z In(05) vcpu-0 SVGA: Registering IOSpace at 0x1070
2024-01-20T08:10:21.250Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-01-20T08:10:21.250Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-01-20T08:10:21.274Z In(05) vcpu-0 UHCI: Global Reset
2024-01-20T08:10:21.539Z In(05) vmx USB: Connecting device desc:name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4 id:0x700000010e0f0008
2024-01-20T08:10:21.539Z Wa(03) vmx Bluetooth-Win32: ERROR retrieving local radio info: No more data is available
2024-01-20T08:10:21.539Z Wa(03) vmx Bluetooth host backend not available.
2024-01-20T08:10:21.596Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2024-01-20T08:10:21.596Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:10:21.596Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2024-01-20T08:10:21.596Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2024-01-20T08:10:21.596Z In(05) vcpu-0 CDROM: Unknown command 0xA4.
2024-01-20T08:10:21.596Z In(05) vcpu-0 CDROM sata0:1: CMD 0xa4 (*UNKNOWN (0xa4)*) FAILED (key 0x5 asc 0x20 ascq 0)
2024-01-20T08:10:21.611Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 1 starting feature 0
2024-01-20T08:10:21.669Z Wa(03) vcpu-0 HDAudio: HDAudioControllerReset: RIRB run bit is set.
2024-01-20T08:10:35.422Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:10:35.423Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:10:35.423Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 263
2024-01-20T08:10:35.423Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 263
2024-01-20T08:10:36.480Z In(05) vcpu-0 Tools: Tools heartbeat timeout.
2024-01-20T08:10:36.480Z In(05) vcpu-0 Tools: Running status rpc handler: 1 => 0.
2024-01-20T08:10:36.480Z In(05) vcpu-0 Tools: Changing running status: 1 => 0.
2024-01-20T08:10:36.480Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 1 (last received 20s ago)
2024-01-20T08:10:36.858Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:10:43.687Z In(05) vcpu-0 Unknown int 10h func 0x0000
2024-01-20T08:10:51.540Z In(05) vmx USB: Connecting device desc:name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4 id:0x700000010e0f0008
2024-01-20T08:10:51.540Z Wa(03) vmx Bluetooth-Win32: ERROR retrieving local radio info: No more data is available
2024-01-20T08:10:51.540Z Wa(03) vmx Bluetooth host backend not available.
2024-01-20T08:10:56.667Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:10:56.667Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 30
2024-01-20T08:10:56.667Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:10:56.667Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 32
2024-01-20T08:10:56.667Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 33
2024-01-20T08:10:56.667Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 35
2024-01-20T08:10:56.667Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 36
2024-01-20T08:10:56.667Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 38
2024-01-20T08:10:56.667Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:10:56.667Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:10:56.667Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:10:56.668Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 46
2024-01-20T08:10:56.668Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:10:56.668Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:10:56.668Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:10:56.668Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 259
2024-01-20T08:10:59.704Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:59.704Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:59.757Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:59.757Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:59.762Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:59.763Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:59.767Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:59.767Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:59.772Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:59.772Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:59.776Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:10:59.776Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:00.449Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:11:04.227Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:04.227Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:04.229Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:04.229Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:04.381Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:04.382Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:04.384Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:04.384Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:04.385Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:04.385Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:04.386Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:04.386Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:05.349Z In(05) vcpu-0 Guest: Driver=vmhgfs, Version=11.0.42.0
2024-01-20T08:11:05.480Z In(05) vcpu-0 Tools: Running status rpc handler: 0 => 1.
2024-01-20T08:11:05.480Z In(05) vcpu-0 Tools: Changing running status: 0 => 1.
2024-01-20T08:11:05.480Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 2 (last received 0s ago)
2024-01-20T08:11:05.829Z In(05) vcpu-0 Guest: toolbox: Version: 11.3.5.31214 (build-18557794)
2024-01-20T08:11:05.831Z Wa(03) vcpu-0 GuestRpc: application toolbox, changing channel 65535 -> 0
2024-01-20T08:11:05.831Z In(05) vcpu-0 GuestRpc: Channel 0, guest application toolbox.
2024-01-20T08:11:05.831Z In(05) vcpu-0 TOOLS Reducing idleLoopSpinUS to 500us
2024-01-20T08:11:05.831Z In(05) vcpu-0 Tools: [AppStatus] Last heartbeat value 2 (last received 0s ago)
2024-01-20T08:11:05.831Z In(05) vcpu-0 TOOLS: appName=toolbox, oldStatus=0, status=1, guestInitiated=0.
2024-01-20T08:11:05.885Z In(05) vcpu-0 TOOLS autoupgrade protocol version 2
2024-01-20T08:11:05.885Z In(05) vcpu-0 Tools: Changing running status: 1 => 2.
2024-01-20T08:11:05.885Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 2 (last received 0s ago)
2024-01-20T08:11:05.890Z In(05) vcpu-0 TOOLS Received tools.set.version rpc call, version = 11365, setting type to 1 from guest OS
2024-01-20T08:11:05.890Z In(05) vcpu-0 Tools_SetVersionAndType did nothing; new tools version (11365) and type (1) match old Tools version and type
2024-01-20T08:11:05.891Z In(05) vcpu-0 ToolsISO: Refreshing imageName for 'windows7' (refreshCount=1, lastCount=1).
2024-01-20T08:11:05.891Z In(05) vcpu-0 ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-01-20T08:11:05.891Z In(05) vcpu-0 ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-01-20T08:11:05.893Z In(05) vcpu-0 ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-01-20T08:11:05.893Z In(05) vcpu-0 ToolsISO: Selected Tools ISO 'windows.iso' for 'windows7' guest.
2024-01-20T08:11:05.893Z In(05) vcpu-0 TOOLS updated cached value for isoImageExists to 1.
2024-01-20T08:11:05.893Z In(05) vcpu-0 Starting copy guest manifest.
2024-01-20T08:11:05.897Z In(05) vcpu-0 Guest: Executing script for state change 'OS_PowerOn'.
2024-01-20T08:11:05.904Z In(05) vcpu-0 Tools: State change '3' progress: last event 0, event 1, success 1.
2024-01-20T08:11:05.904Z In(05) vcpu-0 HGFileCopyCreateSessionCB: Successfully created the session.
2024-01-20T08:11:06.004Z In(05) vmx Tools_SetGuestResolution: Sending rpcMsg = Resolution_Set 1078 862
2024-01-20T08:11:06.749Z In(05) vcpu-0 TOOLS call to Resolution_Set failed.
2024-01-20T08:11:06.749Z In(05) mks MKSControlMgr: The display RPC request is failed: Invalid arguments
2024-01-20T08:11:06.751Z In(05) vcpu-0 ToolsLoadManifestFileCB: Queuing a request to update the manifest information.
2024-01-20T08:11:06.752Z In(05) worker-5020 ToolsISO: Refreshing imageName for 'windows7' (refreshCount=1, lastCount=1).
2024-01-20T08:11:06.753Z In(05) worker-5020 ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-01-20T08:11:06.753Z In(05) worker-5020 ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-01-20T08:11:06.754Z In(05) worker-5020 ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-01-20T08:11:06.754Z In(05) worker-5020 ToolsISO: Selected Tools ISO 'windows.iso' for 'windows7' guest.
2024-01-20T08:11:06.754Z In(05) worker-5020 GetHostManifests: Extracting C:\Users\<USER>\AppData\Local\Temp\vmware-bfdemo\manifest.txt.5020.iso.shipped manifest file.
2024-01-20T08:11:06.754Z In(05) worker-5020 ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\windows.iso.sha failed: Could not find the file
2024-01-20T08:11:06.754Z In(05) worker-5020 ToolsISO: Unable to read hash file C:\Program Files (x86)\VMware\VMware Workstation\windows.iso.sha, ignoring.
2024-01-20T08:11:06.756Z In(05) worker-5020 GetHostManifests: Done extracting the manifest file.
2024-01-20T08:11:06.756Z In(05) worker-5020 Win32U_GetFileAttributes: GetFileAttributesExW("C:\Program Files (x86)\VMware\VMware Workstation\windows_avr_manifest.txt", ...) failed, error: 2
2024-01-20T08:11:06.758Z In(05) worker-5020 Using ToolsMinVersion = 8384
2024-01-20T08:11:06.758Z In(05) worker-5020 ToolsVersionGetStatusWorkerThread: Tools status 3 derived from environment
2024-01-20T08:11:06.758Z In(05) vmx ToolsUpdateManifestInfoWorkerThreadDone: Compared tools manifest from host and from the guest. Status = 3.
2024-01-20T08:11:06.758Z In(05) vmx ToolsUpdateManifestInfoWorkerThreadDone: Updating the manifest info.
2024-01-20T08:11:06.759Z No(00) vmx ConfigDB: Setting extendedConfigFile = "Windows 7 的克隆.vmxf"
2024-01-20T08:11:06.777Z In(05) vmx ToolsISO: Refreshing imageName for 'windows7' (refreshCount=1, lastCount=1).
2024-01-20T08:11:06.777Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-01-20T08:11:06.777Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-01-20T08:11:06.778Z In(05) vmx ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-01-20T08:11:06.778Z In(05) vmx ToolsISO: Selected Tools ISO 'windows.iso' for 'windows7' guest.
2024-01-20T08:11:06.778Z In(05) vmx TOOLS updated cached value for isoImageExists to 1.
2024-01-20T08:11:06.778Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'ok', 'current', install possible
2024-01-20T08:11:06.778Z In(05) vmx TOOLS installed legacy version 11365, available legacy version 11365
2024-01-20T08:11:06.778Z In(05) vmx TOOLS manifest update status is 3
2024-01-20T08:11:06.778Z In(05) vmx TOOLS can be autoupgraded.
2024-01-20T08:11:06.778Z In(05) vmx TOOLS Setting autoupgrade-checked TRUE.
2024-01-20T08:11:08.013Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-01-20T08:11:11.145Z In(05) vcpu-0 Guest: Script exit code: 0, success = 1
2024-01-20T08:11:11.146Z In(05) vcpu-0 TOOLS state change 3 returned status 1
2024-01-20T08:11:11.146Z In(05) vcpu-0 Tools: State change '3' progress: last event 1, event 2, success 1.
2024-01-20T08:11:11.146Z In(05) vcpu-0 Tools: State change '3' progress: last event 1, event 4, success 1.
2024-01-20T08:11:11.146Z In(05) vcpu-0 Vix: [mainDispatch.c:4123]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2024-01-20T08:11:11.146Z In(05) vcpu-0 Tools: Changing running status: 2 => 1.
2024-01-20T08:11:11.146Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 7 (last received 0s ago)
2024-01-20T08:11:17.020Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:17.020Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:17.021Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:17.021Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:17.087Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:17.087Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:17.089Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:17.090Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:17.092Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:17.093Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:17.093Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:17.093Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.100Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.100Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.110Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.110Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.162Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.162Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.164Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.164Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.166Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.166Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.403Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.404Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.405Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.405Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.406Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:20.406Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:30.761Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:30.762Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:35.816Z In(05) vcpu-0 GuestInfo: HostinfoDetailedDataHeader version: 1
2024-01-20T08:11:35.816Z No(00) vcpu-0 ConfigDB: Setting guestInfo.detailed.data = <not printed>
2024-01-20T08:11:37.362Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:11:37.363Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:11:41.663Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:11:43.074Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:43.074Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:44.330Z In(05) vcpu-0 HgfsChannelBdConnectInternal: Backdoor HGFS server session init complete and opened.
2024-01-20T08:11:44.330Z In(05) vcpu-0 HgfsServerAllocateSession: init session 1AD58601C70 id 173b05dc087
2024-01-20T08:11:52.163Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:11:52.200Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:11:52.200Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:11:52.219Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:11:52.220Z In(05) soundWavePlayback0 VTHREAD 3404 "soundWavePlayback0"
2024-01-20T08:11:52.220Z In(05) soundWavePlayback0 SOUNDLIB: SoundWaveThread: Poll thread entered : id(3404)
2024-01-20T08:11:52.220Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(3404)
2024-01-20T08:11:52.231Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD54FC5A10.
2024-01-20T08:11:52.489Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.489Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.491Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.492Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.492Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:11:52.492Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:11:52.492Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:11:52.493Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:11:52.493Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:11:52.493Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:11:52.493Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:11:52.493Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:11:52.493Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:11:52.493Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:11:52.493Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:11:52.493Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:11:52.493Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.493Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.499Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.499Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.500Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.501Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.501Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.501Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.501Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.502Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.502Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.502Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.502Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.502Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.502Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.502Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.502Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.502Z In(05) vcpu-0 CDROM sata0:1: CMD 0xad (*UNKNOWN (0xad)*) FAILED (key 0x5 asc 0x24 ascq 0)
2024-01-20T08:11:52.504Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.504Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.505Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.505Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.505Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.505Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.505Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.505Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.506Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.506Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.506Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.506Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.506Z In(05) vcpu-0 CDROM sata0:1: CMD 0xad (*UNKNOWN (0xad)*) FAILED (key 0x5 asc 0x24 ascq 0)
2024-01-20T08:11:52.507Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.507Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.507Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.507Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.507Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.508Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.508Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.508Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.508Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.508Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.508Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.508Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.508Z In(05) vcpu-0 CDROM sata0:1: CMD 0xad (*UNKNOWN (0xad)*) FAILED (key 0x5 asc 0x24 ascq 0)
2024-01-20T08:11:52.509Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.509Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.509Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.509Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.510Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.510Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.510Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.510Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.510Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.510Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-01-20T08:11:52.510Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.510Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-01-20T08:11:52.511Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.511Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.511Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:11:52.511Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.512Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:11:52.526Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 33
2024-01-20T08:11:52.526Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 33
2024-01-20T08:11:52.526Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 32
2024-01-20T08:11:52.526Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 32
2024-01-20T08:11:52.527Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 38
2024-01-20T08:11:52.527Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 38
2024-01-20T08:11:52.527Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:11:52.527Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:11:52.527Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 44
2024-01-20T08:11:52.527Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 44
2024-01-20T08:11:52.527Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 33
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 33
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 32
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 32
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 38
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 38
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:11:52.528Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:11:52.529Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 44
2024-01-20T08:11:52.529Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 44
2024-01-20T08:11:52.529Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:11:52.529Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:11:52.529Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:11:52.529Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:11:52.529Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:11:52.530Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:11:52.814Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:52.815Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:55.511Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:11:57.079Z In(05) vcpu-0 ALC885: ALC885_AttenuateSamples: Error in software mixer. Software mixer is disabled for output sound streams.
2024-01-20T08:11:57.095Z In(05) vcpu-0 SOUNDLIB: Stopping WAVE stream 1AD54FC5A10.
2024-01-20T08:11:57.099Z In(05) soundWavePlayback0 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:11:57.099Z In(05) vcpu-0 SOUNDLIB: Closing WAVE stream 1AD54FC5A10.
2024-01-20T08:11:57.099Z In(05) vcpu-0 SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(3404)
2024-01-20T08:11:57.099Z In(05) soundWavePlayback0 SOUNDLIB: SoundWaveThread: Poll thread exited : id(3404)
2024-01-20T08:11:57.482Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:11:57.482Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:00.170Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:12:00.431Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:12:00.599Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:12:00.959Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:12:01.407Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:12:01.466Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.466Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.468Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.468Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.553Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.554Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.603Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.603Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.607Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.607Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.608Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.608Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:01.801Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:12:01.801Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:12:03.822Z In(05) vmx DISKLIB-LIB   : numIOs = 50000 numMergedIOs = 11375 numSplitIOs = 895
2024-01-20T08:12:04.288Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:04.288Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:06.779Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-01-20T08:12:22.248Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:22.248Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:23.366Z In(05) vcpu-0 Guest: toolbox-dnd: Version: 11.3.5.31214 (build-18557794)
2024-01-20T08:12:23.627Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:12:23.641Z Wa(03) vcpu-0 GuestRpc: application toolbox-dnd, changing channel 65535 -> 3
2024-01-20T08:12:23.641Z In(05) vcpu-0 GuestRpc: Channel 3, guest application toolbox-dnd.
2024-01-20T08:12:23.641Z In(05) vcpu-0 TOOLS: appName=toolbox-dnd, oldStatus=0, status=1, guestInitiated=0.
2024-01-20T08:12:23.665Z In(05) vcpu-0 DnDCP: dndGuestVersion from vmdb failed, setting to 4
2024-01-20T08:12:23.665Z In(05) vcpu-0 DnDCP: set guest controllers to version 4
2024-01-20T08:12:23.669Z In(05) vcpu-0 DnDCP: set guest controllers to version 4
2024-01-20T08:12:23.669Z In(05) vcpu-0 DnDCP: set guest controllers to version 4
2024-01-20T08:12:23.671Z No(00) vcpu-0 ConfigDB: Setting unity.wasCapable = "TRUE"
2024-01-20T08:12:24.346Z In(05) vcpu-0 DnDCP: set guest controllers to version 4
2024-01-20T08:12:30.083Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:12:30.085Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:12:30.085Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:12:30.101Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:12:30.102Z In(05) soundWavePlayback1 VTHREAD 7852 "soundWavePlayback1"
2024-01-20T08:12:30.102Z In(05) soundWavePlayback1 SOUNDLIB: SoundWaveThread: Poll thread entered : id(7852)
2024-01-20T08:12:30.102Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(7852)
2024-01-20T08:12:30.113Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57573A30.
2024-01-20T08:12:32.570Z In(05) vcpu-0 SOUNDLIB: Stopping WAVE stream 1AD57573A30.
2024-01-20T08:12:32.572Z In(05) soundWavePlayback1 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:12:32.572Z In(05) vcpu-0 SOUNDLIB: Closing WAVE stream 1AD57573A30.
2024-01-20T08:12:32.572Z In(05) vcpu-0 SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(7852)
2024-01-20T08:12:32.572Z In(05) soundWavePlayback1 SOUNDLIB: SoundWaveThread: Poll thread exited : id(7852)
2024-01-20T08:12:37.904Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:37.904Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:43.644Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-01-20T08:12:43.644Z In(05) vmx TOOLS: appName=toolbox-dnd, oldStatus=1, status=2, guestInitiated=0.
2024-01-20T08:12:50.428Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:12:50.431Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:12:50.431Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:12:50.447Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:12:50.448Z In(05) soundWavePlayback2 VTHREAD 14896 "soundWavePlayback2"
2024-01-20T08:12:50.448Z In(05) soundWavePlayback2 SOUNDLIB: SoundWaveThread: Poll thread entered : id(14896)
2024-01-20T08:12:50.448Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(14896)
2024-01-20T08:12:50.459Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57572BF0.
2024-01-20T08:12:50.628Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:50.628Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:12:50.794Z In(05) vcpu-0 TOOLS: appName=toolbox-dnd, oldStatus=2, status=1, guestInitiated=0.
2024-01-20T08:12:53.045Z In(05) vcpu-0 SOUNDLIB: Stopping WAVE stream 1AD57572BF0.
2024-01-20T08:12:53.050Z In(05) soundWavePlayback2 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:12:53.050Z In(05) vcpu-0 SOUNDLIB: Closing WAVE stream 1AD57572BF0.
2024-01-20T08:12:53.050Z In(05) vcpu-0 SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(14896)
2024-01-20T08:12:53.050Z In(05) soundWavePlayback2 SOUNDLIB: SoundWaveThread: Poll thread exited : id(14896)
2024-01-20T08:13:14.870Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:13:14.870Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:13:22.338Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:13:24.913Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:13:26.684Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:13:26.684Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:13:27.556Z In(05) vmx DISKLIB-LIB   : numIOs = 100000 numMergedIOs = 15512 numSplitIOs = 1496
2024-01-20T08:13:31.423Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:13:31.426Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:13:31.426Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:13:31.442Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:13:31.442Z In(05) soundWavePlayback3 VTHREAD 10296 "soundWavePlayback3"
2024-01-20T08:13:31.442Z In(05) soundWavePlayback3 SOUNDLIB: SoundWaveThread: Poll thread entered : id(10296)
2024-01-20T08:13:31.442Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(10296)
2024-01-20T08:13:31.454Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD575730B0.
2024-01-20T08:13:32.384Z In(05) vmx SOUNDLIB: Stopping WAVE stream 1AD575730B0.
2024-01-20T08:13:32.395Z In(05) soundWavePlayback3 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:13:32.397Z In(05) vmx SOUNDLIB: Closing WAVE stream 1AD575730B0.
2024-01-20T08:13:32.397Z In(05) vmx SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(10296)
2024-01-20T08:13:32.397Z In(05) soundWavePlayback3 SOUNDLIB: SoundWaveThread: Poll thread exited : id(10296)
2024-01-20T08:13:33.149Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:13:33.149Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:13:36.229Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:13:36.229Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:13:42.211Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:13:42.213Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:13:42.213Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:13:42.229Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:13:42.230Z In(05) soundWavePlayback4 VTHREAD 3980 "soundWavePlayback4"
2024-01-20T08:13:42.230Z In(05) soundWavePlayback4 SOUNDLIB: SoundWaveThread: Poll thread entered : id(3980)
2024-01-20T08:13:42.230Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(3980)
2024-01-20T08:13:42.241Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57573310.
2024-01-20T08:13:42.327Z In(05) vmx SOUNDLIB: Stopping WAVE stream 1AD57573310.
2024-01-20T08:13:42.339Z In(05) soundWavePlayback4 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:13:42.340Z In(05) vmx SOUNDLIB: Closing WAVE stream 1AD57573310.
2024-01-20T08:13:42.340Z In(05) vmx SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(3980)
2024-01-20T08:13:42.340Z In(05) soundWavePlayback4 SOUNDLIB: SoundWaveThread: Poll thread exited : id(3980)
2024-01-20T08:13:48.775Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:13:48.778Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:13:48.778Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:13:48.793Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:13:48.794Z In(05) soundWavePlayback5 VTHREAD 6224 "soundWavePlayback5"
2024-01-20T08:13:48.794Z In(05) soundWavePlayback5 SOUNDLIB: SoundWaveThread: Poll thread entered : id(6224)
2024-01-20T08:13:48.794Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(6224)
2024-01-20T08:13:48.805Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57573310.
2024-01-20T08:13:48.892Z In(05) vcpu-0 SOUNDLIB: Stopping WAVE stream 1AD57573310.
2024-01-20T08:13:48.893Z In(05) vcpu-0 SOUNDLIB: Closing WAVE stream 1AD57573310.
2024-01-20T08:13:48.893Z In(05) vcpu-0 SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(6224)
2024-01-20T08:13:48.893Z In(05) soundWavePlayback5 SOUNDLIB: SoundWaveThread: Poll thread exited : id(6224)
2024-01-20T08:14:01.909Z In(05) vmx Tools_SetGuestResolution: Sending rpcMsg = Resolution_Set 1280 1024
2024-01-20T08:14:06.882Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:14:06.885Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:14:06.885Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:14:06.901Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:14:06.902Z In(05) soundWavePlayback6 VTHREAD 7284 "soundWavePlayback6"
2024-01-20T08:14:06.902Z In(05) soundWavePlayback6 SOUNDLIB: SoundWaveThread: Poll thread entered : id(7284)
2024-01-20T08:14:06.902Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(7284)
2024-01-20T08:14:06.913Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57572730.
2024-01-20T08:14:06.988Z In(05) vmx SOUNDLIB: Stopping WAVE stream 1AD57572730.
2024-01-20T08:14:06.999Z In(05) soundWavePlayback6 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:14:06.999Z In(05) vmx SOUNDLIB: Closing WAVE stream 1AD57572730.
2024-01-20T08:14:06.999Z In(05) vmx SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(7284)
2024-01-20T08:14:06.999Z In(05) soundWavePlayback6 SOUNDLIB: SoundWaveThread: Poll thread exited : id(7284)
2024-01-20T08:14:09.724Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:14:09.726Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:14:09.726Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:14:09.742Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:14:09.742Z In(05) soundWavePlayback7 VTHREAD 16080 "soundWavePlayback7"
2024-01-20T08:14:09.742Z In(05) soundWavePlayback7 SOUNDLIB: SoundWaveThread: Poll thread entered : id(16080)
2024-01-20T08:14:09.742Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(16080)
2024-01-20T08:14:09.752Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD575737D0.
2024-01-20T08:14:09.840Z In(05) vmx SOUNDLIB: Stopping WAVE stream 1AD575737D0.
2024-01-20T08:14:09.851Z In(05) soundWavePlayback7 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:14:09.852Z In(05) vmx SOUNDLIB: Closing WAVE stream 1AD575737D0.
2024-01-20T08:14:09.852Z In(05) vmx SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(16080)
2024-01-20T08:14:09.852Z In(05) soundWavePlayback7 SOUNDLIB: SoundWaveThread: Poll thread exited : id(16080)
2024-01-20T08:14:20.499Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:14:20.501Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:14:20.501Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:14:20.517Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:14:20.518Z In(05) soundWavePlayback8 VTHREAD 2352 "soundWavePlayback8"
2024-01-20T08:14:20.518Z In(05) soundWavePlayback8 SOUNDLIB: SoundWaveThread: Poll thread entered : id(2352)
2024-01-20T08:14:20.518Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(2352)
2024-01-20T08:14:20.529Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57573EF0.
2024-01-20T08:14:20.812Z In(05) vmx SOUNDLIB: Stopping WAVE stream 1AD57573EF0.
2024-01-20T08:14:20.823Z In(05) soundWavePlayback8 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:14:20.823Z In(05) vmx SOUNDLIB: Closing WAVE stream 1AD57573EF0.
2024-01-20T08:14:20.823Z In(05) vmx SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(2352)
2024-01-20T08:14:20.823Z In(05) soundWavePlayback8 SOUNDLIB: SoundWaveThread: Poll thread exited : id(2352)
2024-01-20T08:14:22.424Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:14:22.427Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:14:22.427Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:14:22.444Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:14:22.445Z In(05) soundWavePlayback9 VTHREAD 15872 "soundWavePlayback9"
2024-01-20T08:14:22.445Z In(05) soundWavePlayback9 SOUNDLIB: SoundWaveThread: Poll thread entered : id(15872)
2024-01-20T08:14:22.445Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(15872)
2024-01-20T08:14:22.456Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57573440.
2024-01-20T08:14:22.549Z In(05) vcpu-0 SOUNDLIB: Stopping WAVE stream 1AD57573440.
2024-01-20T08:14:22.553Z In(05) soundWavePlayback9 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:14:22.555Z In(05) vcpu-0 SOUNDLIB: Closing WAVE stream 1AD57573440.
2024-01-20T08:14:22.555Z In(05) vcpu-0 SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(15872)
2024-01-20T08:14:22.555Z In(05) soundWavePlayback9 SOUNDLIB: SoundWaveThread: Poll thread exited : id(15872)
2024-01-20T08:14:27.116Z In(05) vmx VUsbUpdateVigorFieldsAndAutoconnect: New set of 2 USB devices
2024-01-20T08:14:27.116Z In(05) vmx USB: Found device [name:Disk\ 2.0 vid:346d pid:5678 path:1/4/4 speed:high family:storage,storage-bulk instanceId:USB\\VID_0E0F&PID_0001\\0275111243782046049 serialnum:0275111243782046049 arbRuntimeKey:2 version:4]
2024-01-20T08:14:27.116Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4]
2024-01-20T08:14:32.759Z In(05) vmx USBGA: Device 10000002346d5678 arrived
2024-01-20T08:14:32.759Z In(05) vmx USBGA: Autoconnecting new device
2024-01-20T08:14:32.759Z In(05) vmx USB: Connecting device desc:name:Disk\ 2.0 vid:346d pid:5678 path:1/4/4 speed:high family:storage,storage-bulk instanceId:USB\\VID_0E0F&PID_0001\\0275111243782046049 serialnum:0275111243782046049 arbRuntimeKey:2 version:4 id:0x10000002346d5678
2024-01-20T08:14:32.760Z In(05) vmx USBG: Created 10000002346d5678
2024-01-20T08:14:32.760Z In(05) vmx USBGW: Disabling USB 3.0 streams
2024-01-20T08:14:32.760Z In(05) vmx VUsbUpdateVigorFieldsAndAutoconnect: New set of 2 USB devices
2024-01-20T08:14:32.760Z In(05) vmx USB: Found device [name:Disk\ 2.0 vid:346d pid:5678 path:1/4/4 speed:high family:storage,storage-bulk virtPath:ehci:0 instanceId:USB\\VID_0E0F&PID_0001\\0275111243782046049 serialnum:0275111243782046049 arbRuntimeKey:2 version:4], connected to ehci port 0.
2024-01-20T08:14:32.760Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4]
2024-01-20T08:14:32.761Z In(05) vmx VUsbUpdateVigorFieldsAndAutoconnect: New set of 2 USB devices
2024-01-20T08:14:32.761Z In(05) vmx USB: Found device [name:Disk\ 2.0 vid:346d pid:5678 path:1/4/4 speed:high family:storage,storage-bulk virtPath:ehci:0 instanceId:USB\\VID_0E0F&PID_0001\\0275111243782046049 serialnum:0275111243782046049 arbRuntimeKey:2 version:4], connected to ehci port 0.
2024-01-20T08:14:32.761Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4]
2024-01-20T08:14:33.250Z In(05) vmx USBIO: Detected usb-storage class
2024-01-20T08:14:33.301Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:14:33.304Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:14:33.304Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:14:33.321Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:14:33.321Z In(05) soundWavePlayback10 VTHREAD 11084 "soundWavePlayback10"
2024-01-20T08:14:33.321Z In(05) soundWavePlayback10 SOUNDLIB: SoundWaveThread: Poll thread entered : id(11084)
2024-01-20T08:14:33.321Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(11084)
2024-01-20T08:14:33.331Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57573310.
2024-01-20T08:14:34.893Z In(05) vcpu-0 SOUNDLIB: Stopping WAVE stream 1AD57573310.
2024-01-20T08:14:34.895Z In(05) soundWavePlayback10 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:14:34.895Z In(05) vcpu-0 SOUNDLIB: Closing WAVE stream 1AD57573310.
2024-01-20T08:14:34.895Z In(05) vcpu-0 SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(11084)
2024-01-20T08:14:34.895Z In(05) soundWavePlayback10 SOUNDLIB: SoundWaveThread: Poll thread exited : id(11084)
2024-01-20T08:14:36.159Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:36.159Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:36.160Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:36.170Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:36.170Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:14:36.170Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:14:36.170Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:14:36.170Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:14:36.171Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:14:36.171Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:14:36.171Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:14:36.171Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:14:36.171Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:14:36.171Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:14:36.172Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:14:36.172Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:14:36.172Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:36.172Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:36.880Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:36.881Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:36.881Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:36.881Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:36.881Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:14:36.881Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:14:36.882Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:14:36.883Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:14:36.884Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:14:36.884Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:14:36.884Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:14:36.884Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:14:36.884Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:14:36.884Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:14:36.884Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:14:36.884Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:14:36.884Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:36.885Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:14:40.238Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:14:40.238Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:14:48.397Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:14:48.400Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:14:48.400Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:14:48.420Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:14:48.422Z In(05) soundWavePlayback11 VTHREAD 15500 "soundWavePlayback11"
2024-01-20T08:14:48.422Z In(05) soundWavePlayback11 SOUNDLIB: SoundWaveThread: Poll thread entered : id(15500)
2024-01-20T08:14:48.422Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(15500)
2024-01-20T08:14:48.433Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57572990.
2024-01-20T08:14:48.505Z In(05) vcpu-0 SOUNDLIB: Stopping WAVE stream 1AD57572990.
2024-01-20T08:14:48.508Z In(05) soundWavePlayback11 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:14:48.510Z In(05) vcpu-0 SOUNDLIB: Closing WAVE stream 1AD57572990.
2024-01-20T08:14:48.510Z In(05) vcpu-0 SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(15500)
2024-01-20T08:14:48.510Z In(05) soundWavePlayback11 SOUNDLIB: SoundWaveThread: Poll thread exited : id(15500)
2024-01-20T08:14:51.831Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:14:51.831Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:14:54.037Z In(05) vmx DISKLIB-LIB   : numIOs = 150000 numMergedIOs = 18792 numSplitIOs = 1797
2024-01-20T08:15:09.128Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:09.128Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:29.246Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:29.246Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:32.060Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:32.061Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:35.126Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:35.126Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:37.675Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:37.675Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:48.676Z In(05) vcpu-0 CPClipboard_SetItem: Set CPClipboard struct with data of size:143, format:2.
2024-01-20T08:15:49.351Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:49.351Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:50.030Z In(05) vmx ToolsSetDisplayTopology: Sending rpcMsg = DisplayTopology_Set 1 , 0 0 1280 1024
2024-01-20T08:15:50.051Z In(05) vmx Tools_SetGuestResolution: Sending rpcMsg = Resolution_Set 1074 858
2024-01-20T08:15:51.756Z In(05) vcpu-0 TOOLS call to Resolution_Set failed.
2024-01-20T08:15:51.756Z In(05) mks MKSControlMgr: The display RPC request is failed: Invalid arguments
2024-01-20T08:15:55.768Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2024-01-20T08:15:55.768Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2024-01-20T08:15:55.785Z In(05) vmx VMXVmdb_SetCfgState: cfgReqPath=/vm/#_VMX/vmx/cfgState/req/#16/, remDevPath=/vm/#_VMX/vmx/vigor/setCfgStateReq/#32/in/
2024-01-20T08:15:55.847Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2024-01-20T08:15:55.848Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2024-01-20T08:15:55.873Z No(00) vmx ConfigDB: Setting tools.upgrade.policy = "useGlobal"
2024-01-20T08:15:55.890Z In(05) vmx USB: Disconnecting pattern [path:1/4/4] controller []
2024-01-20T08:15:55.890Z In(05) vmx USB: Disconnecting device 0x10000002346d5678
2024-01-20T08:15:55.890Z In(05) vmx USBG: Disconnecting 10000002346d5678, port:0 reservedPort:0
2024-01-20T08:15:55.890Z In(05) vmx USBGW: Disconnecting device: 1ADDD9A23B8 id: 10000002346d5678
2024-01-20T08:15:55.891Z In(05) vmx VUsbUpdateVigorFieldsAndAutoconnect: New set of 2 USB devices
2024-01-20T08:15:55.891Z In(05) vmx USB: Found device [name:Disk\ 2.0 vid:346d pid:5678 path:1/4/4 speed:high family:storage,storage-bulk instanceId:USB\\VID_0E0F&PID_0001\\0275111243782046049 serialnum:0275111243782046049 arbRuntimeKey:2 ownerdisplay:Windows\ 10\ lonele ownertarget:vmware-vmx:F:\\vm_lonele\\Windows\ 7\ 的克隆.vmx version:4]
2024-01-20T08:15:55.891Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4]
2024-01-20T08:15:55.891Z In(05) vmx VUsbUpdateVigorFieldsAndAutoconnect: New set of 2 USB devices
2024-01-20T08:15:55.891Z In(05) vmx USB: Found device [name:Disk\ 2.0 vid:346d pid:5678 path:1/4/4 speed:high family:storage,storage-bulk instanceId:USB\\VID_0E0F&PID_0001\\0275111243782046049 serialnum:0275111243782046049 disconnected:1 arbRuntimeKey:2 version:4]
2024-01-20T08:15:55.891Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4]
2024-01-20T08:15:55.900Z No(00) vmx ConfigDB: Unsetting "usb.generic.pluginAction"
2024-01-20T08:15:56.091Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.091Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.092Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.092Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.092Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:15:56.092Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:15:56.093Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:15:56.093Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:15:56.093Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:15:56.093Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:15:56.093Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:15:56.093Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:15:56.093Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:15:56.093Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:15:56.093Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:15:56.093Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:15:56.093Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.094Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.118Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.119Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.119Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.119Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.119Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:15:56.119Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-01-20T08:15:56.119Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:15:56.120Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-01-20T08:15:56.120Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:15:56.120Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-01-20T08:15:56.120Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:15:56.125Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-01-20T08:15:56.125Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:15:56.125Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-01-20T08:15:56.125Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:15:56.125Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-01-20T08:15:56.126Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.126Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-01-20T08:15:56.199Z In(05) vmx VUsbUpdateVigorFieldsAndAutoconnect: New set of 2 USB devices
2024-01-20T08:15:56.199Z In(05) vmx USB: Found device [name:Disk\ 2.0 vid:346d pid:5678 path:1/4/4 speed:high family:storage,storage-bulk instanceId:USB\\VID_346D&PID_5678\\0275111243782046049 serialnum:0275111243782046049 arbRuntimeKey:2 version:4]
2024-01-20T08:15:56.199Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4]
2024-01-20T08:15:58.892Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:15:58.895Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:15:58.895Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:15:58.912Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:15:58.913Z In(05) soundWavePlayback12 VTHREAD 15872 "soundWavePlayback12"
2024-01-20T08:15:58.913Z In(05) soundWavePlayback12 SOUNDLIB: SoundWaveThread: Poll thread entered : id(15872)
2024-01-20T08:15:58.913Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(15872)
2024-01-20T08:15:58.925Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57572E50.
2024-01-20T08:15:59.794Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:15:59.794Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:16:00.399Z In(05) vmx SOUNDLIB: Stopping WAVE stream 1AD57572E50.
2024-01-20T08:16:00.409Z In(05) soundWavePlayback12 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:16:00.411Z In(05) vmx SOUNDLIB: Closing WAVE stream 1AD57572E50.
2024-01-20T08:16:00.411Z In(05) vmx SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(15872)
2024-01-20T08:16:00.411Z In(05) soundWavePlayback12 SOUNDLIB: SoundWaveThread: Poll thread exited : id(15872)
2024-01-20T08:16:04.021Z In(05) vmx DISKLIB-LIB   : numIOs = 200000 numMergedIOs = 21820 numSplitIOs = 2271
2024-01-20T08:16:04.141Z In(05) vmx VUsbUpdateVigorFieldsAndAutoconnect: New set of 2 USB devices
2024-01-20T08:16:04.141Z In(05) vmx USB: Found device [name:Disk\ 2.0 vid:346d pid:5678 path:1/4/4 speed:high family:storage,storage-bulk instanceId:USB\\VID_346D&PID_5678\\0275111243782046049 serialnum:0275111243782046049 disconnected:1 arbRuntimeKey:2 version:4]
2024-01-20T08:16:04.141Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4]
2024-01-20T08:16:04.753Z In(05) vmx VUsbUpdateVigorFieldsAndAutoconnect: New set of 1 USB devices
2024-01-20T08:16:04.753Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:4]
2024-01-20T08:16:26.820Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:16:26.820Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:16:58.754Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:16:58.755Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:17:11.885Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:17:11.888Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:17:11.888Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:17:11.904Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:17:11.904Z In(05) soundWavePlayback13 VTHREAD 8292 "soundWavePlayback13"
2024-01-20T08:17:11.904Z In(05) soundWavePlayback13 SOUNDLIB: SoundWaveThread: Poll thread entered : id(8292)
2024-01-20T08:17:11.904Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(8292)
2024-01-20T08:17:11.916Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57573EF0.
2024-01-20T08:17:12.619Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:17:12.619Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:17:15.147Z In(05) vmx SOUNDLIB: Stopping WAVE stream 1AD57573EF0.
2024-01-20T08:17:15.158Z In(05) soundWavePlayback13 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:17:15.158Z In(05) vmx SOUNDLIB: Closing WAVE stream 1AD57573EF0.
2024-01-20T08:17:15.158Z In(05) vmx SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(8292)
2024-01-20T08:17:15.158Z In(05) soundWavePlayback13 SOUNDLIB: SoundWaveThread: Poll thread exited : id(8292)
2024-01-20T08:17:30.988Z In(05) vmx DISKLIB-LIB   : numIOs = 250000 numMergedIOs = 23575 numSplitIOs = 3156
2024-01-20T08:17:52.518Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:17:52.520Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:17:52.520Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:17:52.536Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:17:52.537Z In(05) soundWavePlayback14 VTHREAD 7532 "soundWavePlayback14"
2024-01-20T08:17:52.537Z In(05) soundWavePlayback14 SOUNDLIB: SoundWaveThread: Poll thread entered : id(7532)
2024-01-20T08:17:52.537Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(7532)
2024-01-20T08:17:52.548Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD575744E0.
2024-01-20T08:17:52.789Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:17:52.789Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:17:55.040Z In(05) vmx SOUNDLIB: Stopping WAVE stream 1AD575744E0.
2024-01-20T08:17:55.051Z In(05) soundWavePlayback14 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:17:55.053Z In(05) vmx SOUNDLIB: Closing WAVE stream 1AD575744E0.
2024-01-20T08:17:55.053Z In(05) vmx SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(7532)
2024-01-20T08:17:55.053Z In(05) soundWavePlayback14 SOUNDLIB: SoundWaveThread: Poll thread exited : id(7532)
2024-01-20T08:17:56.762Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:17:56.762Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:00.222Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:00.222Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:02.966Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:02.966Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:05.838Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:05.838Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:08.414Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:08.415Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:11.019Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:11.019Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:13.780Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:13.780Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:16.297Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:16.297Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:16.598Z In(05) vmx DISKLIB-LIB   : numIOs = 300000 numMergedIOs = 24764 numSplitIOs = 3611
2024-01-20T08:18:18.726Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:18.726Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:21.046Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:21.046Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:24.110Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:24.110Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:26.817Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:26.817Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:29.414Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:18:29.414Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:06.026Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:06.027Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:08.813Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:08.813Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:11.688Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:11.688Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:14.245Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:14.246Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:14.583Z In(05) vmx DISKLIB-LIB   : numIOs = 350000 numMergedIOs = 25990 numSplitIOs = 3875
2024-01-20T08:19:17.122Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:17.123Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:19.766Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:19.766Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:22.797Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:22.797Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:25.090Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:25.090Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:27.782Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:27.783Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:30.915Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:30.915Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:33.817Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:33.817Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:36.473Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:36.473Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:39.042Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:39.043Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:39.301Z In(05) vmx DISKLIB-LIB   : numIOs = 400000 numMergedIOs = 26781 numSplitIOs = 4145
2024-01-20T08:19:41.834Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:41.834Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:45.469Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:45.469Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:47.989Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:47.989Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:52.920Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:52.920Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:55.866Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:19:55.866Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:19:58.163Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:19:58.163Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:01.006Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:20:02.883Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:02.883Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:05.614Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:05.615Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:08.200Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:08.201Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:09.920Z In(05) vmx DISKLIB-LIB   : numIOs = 450000 numMergedIOs = 27935 numSplitIOs = 4398
2024-01-20T08:20:12.607Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:12.607Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:15.289Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:15.289Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:17.801Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:17.801Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:20.379Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:20.379Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:23.179Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:23.179Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:25.747Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:25.747Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:29.312Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:20:29.900Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:29.901Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:32.341Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:20:32.555Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:20:32.768Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:20:32.973Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:20:33.182Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:20:33.392Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:20:33.689Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:33.689Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:36.291Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:36.291Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:38.759Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:38.759Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:39.183Z In(05) vmx DISKLIB-LIB   : numIOs = 500000 numMergedIOs = 28995 numSplitIOs = 4708
2024-01-20T08:20:59.498Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:20:59.498Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:02.043Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:02.043Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:04.674Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:04.674Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:07.023Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:07.024Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:09.632Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:09.632Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:12.167Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:12.167Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:14.618Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:14.618Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:16.264Z In(05) vcpu-0 SCSI scsi0:0: Unsupported command LOG SENSE issued. --ok
2024-01-20T08:21:16.954Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:16.955Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:18.958Z In(05) vmx DISKLIB-LIB   : numIOs = 550000 numMergedIOs = 29824 numSplitIOs = 4981
2024-01-20T08:21:19.748Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:19.748Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:37.127Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:37.127Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:39.756Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:39.756Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:42.767Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:42.767Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:45.341Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:45.341Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:48.350Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:48.350Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:50.773Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:50.773Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:53.313Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:53.313Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:55.782Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:55.783Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:59.014Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:59.014Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:21:59.590Z In(05) vmx DISKLIB-LIB   : numIOs = 600000 numMergedIOs = 30956 numSplitIOs = 5342
2024-01-20T08:22:01.447Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:01.448Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:03.922Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:03.922Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:06.491Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:06.491Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:09.352Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:09.352Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:12.301Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:12.301Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:14.913Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:14.914Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:17.347Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:17.347Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:24.194Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:24.194Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:27.787Z In(05) vmx DISKLIB-LIB   : numIOs = 650000 numMergedIOs = 34514 numSplitIOs = 5760
2024-01-20T08:22:29.456Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:29.456Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:31.871Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:31.871Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:34.117Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:34.117Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:36.695Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:36.695Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:39.310Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:39.310Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:41.673Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:41.673Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:44.171Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:44.171Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:46.766Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:46.766Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:49.373Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:49.373Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:51.133Z In(05) vmx DISKLIB-LIB   : numIOs = 700000 numMergedIOs = 35889 numSplitIOs = 6032
2024-01-20T08:22:51.804Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:51.804Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:54.141Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:54.141Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:56.504Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:56.504Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:58.792Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:22:58.792Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:01.234Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:01.234Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:03.551Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:03.551Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:05.867Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:05.868Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:08.342Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:08.342Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:10.657Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:10.657Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:13.101Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:13.102Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:14.075Z In(05) vmx DISKLIB-LIB   : numIOs = 750000 numMergedIOs = 36760 numSplitIOs = 6324
2024-01-20T08:23:15.394Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:15.394Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:17.688Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:17.689Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:20.056Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:20.056Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:22.555Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:22.555Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:24.868Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:24.868Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:27.151Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:27.151Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:29.596Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:29.596Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:32.075Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:32.075Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:34.533Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:34.534Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:36.326Z In(05) vmx DISKLIB-LIB   : numIOs = 800000 numMergedIOs = 37584 numSplitIOs = 6632
2024-01-20T08:23:36.946Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:36.946Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:39.420Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:39.420Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:41.758Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:41.758Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:44.082Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:44.082Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:46.638Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:46.638Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:48.976Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:48.976Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:51.577Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:51.578Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:54.321Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:54.321Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:56.724Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:56.724Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:59.235Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:59.236Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:23:59.512Z In(05) vmx DISKLIB-LIB   : numIOs = 850000 numMergedIOs = 38451 numSplitIOs = 6956
2024-01-20T08:24:01.754Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:01.754Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:04.423Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:04.423Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:06.752Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:06.752Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:09.160Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:09.160Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:11.800Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:11.800Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:14.311Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:14.311Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:16.696Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:16.696Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:19.349Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:19.349Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:22.001Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:22.001Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:22.222Z In(05) vmx DISKLIB-LIB   : numIOs = 900000 numMergedIOs = 39360 numSplitIOs = 7199
2024-01-20T08:24:24.557Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:24.557Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:26.936Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:26.936Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:29.708Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:29.708Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:32.079Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:32.080Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:34.622Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:34.622Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:37.648Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:37.648Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:40.143Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:40.143Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:42.542Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:42.543Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:44.934Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:44.934Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:46.201Z In(05) vmx DISKLIB-LIB   : numIOs = 950000 numMergedIOs = 40258 numSplitIOs = 7462
2024-01-20T08:24:48.220Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:48.220Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:51.241Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:51.241Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:54.164Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:54.164Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:56.768Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:56.768Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:59.232Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:24:59.232Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:01.551Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:01.551Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:04.033Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:04.033Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:06.470Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:06.471Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:08.991Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:08.991Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:09.476Z In(05) vmx DISKLIB-LIB   : numIOs = 1000000 numMergedIOs = 41072 numSplitIOs = 7783
2024-01-20T08:25:11.828Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:11.828Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:14.160Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:14.160Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:16.558Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:16.558Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:18.951Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:18.951Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:21.456Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:21.456Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:23.890Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:23.890Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:26.362Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:26.362Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:28.922Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:28.922Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:31.395Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:31.395Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:31.747Z In(05) vmx DISKLIB-LIB   : numIOs = 1050000 numMergedIOs = 41962 numSplitIOs = 8047
2024-01-20T08:25:33.988Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:33.989Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:36.474Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:36.474Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:38.770Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:38.771Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:41.202Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:41.203Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:43.567Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:43.567Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:45.875Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:45.875Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:48.569Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:48.569Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:51.264Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:51.264Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:54.037Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:54.037Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:25:54.295Z In(05) vmx DISKLIB-LIB   : numIOs = 1100000 numMergedIOs = 43002 numSplitIOs = 8302
2024-01-20T08:26:43.662Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:26:44.700Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:26:44.808Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:26:44.921Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:26:45.037Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:26:45.299Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:26:45.300Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:27:37.765Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:27:37.913Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:27:38.061Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:27:38.211Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:27:38.325Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:28:13.604Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:13.604Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:23.527Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:28:23.645Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:28:23.760Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:28:23.871Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:28:23.981Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:28:27.172Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:27.172Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:29.553Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:29.553Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:29.555Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:29.555Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:29.568Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:29.568Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:29.583Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:29.584Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:29.586Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:28:29.586Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:29:02.468Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:29:02.469Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:29:03.772Z In(05) vmx DISKLIB-LIB   : numIOs = 1150000 numMergedIOs = 49708 numSplitIOs = 9235
2024-01-20T08:31:24.268Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:31:24.268Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:31:52.719Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:31:52.720Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:31:52.720Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=209715200 logical sector size=512
2024-01-20T08:31:52.722Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2024-01-20T08:31:52.723Z In(05) vcpu-0 CDROM sata0:1: CMD 0x52 (*UNKNOWN (0x52)*) FAILED (key 0x5 asc 0x20 ascq 0)
2024-01-20T08:32:02.258Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:32:02.258Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:32:02.260Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:32:02.260Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:32:02.262Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:32:02.262Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:32:02.264Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:32:02.264Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:32:02.265Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:32:02.265Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:32:02.266Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:32:02.266Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.687Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.687Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.688Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.688Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.692Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.692Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.694Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.694Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.696Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.696Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.698Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:39:18.698Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:40:08.096Z In(05) vmx MemSched: caller 0 numvm 1 locked pages: num 527994 max 4120576
2024-01-20T08:40:08.096Z In(05) vmx MemSched: locked Page Limit: host 4474610 config 4128768
2024-01-20T08:40:08.096Z In(05) vmx MemSched: minmempct 50  timestamp 2212
2024-01-20T08:40:08.096Z In(05) vmx MemSched: VM 0 min 272718 max 534862 shares 524288 paged 3957055 nonpaged 4544 anonymous 6030 locked 527994 touchedPct 34 dirtiedPct 6 timestamp 2212 vmResponsive is 1
2024-01-20T08:40:08.096Z In(05) vmx MemSched: locked 527994 target 534862 balloon 0 0 314573 swapped 2164 0 allocd 5 512 state 0 100
2024-01-20T08:40:08.096Z In(05) vmx MemSched: states: 0 1801 : 1 0 : 2 0 : 3 0
2024-01-20T08:40:08.096Z In(05) vmx MemSched: Balloon enabled 1 guestType 4 maxSize 0
2024-01-20T08:41:17.350Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:17.350Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.157Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.158Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.159Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.159Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.163Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.163Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.165Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.165Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.168Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.168Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.176Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:19.176Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:20.559Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:20.559Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:23.061Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:23.061Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:26.192Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:26.193Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:27.640Z In(05) vmx DISKLIB-LIB   : numIOs = 1200000 numMergedIOs = 55128 numSplitIOs = 10074
2024-01-20T08:41:28.758Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:28.758Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:31.208Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:31.208Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:33.743Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:33.743Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:36.434Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:36.434Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:38.950Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:38.950Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:41.729Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:41.729Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:44.250Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:44.250Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:47.850Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:47.850Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:51.383Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:51.384Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:53.194Z In(05) vmx DISKLIB-LIB   : numIOs = 1250000 numMergedIOs = 56272 numSplitIOs = 10399
2024-01-20T08:41:55.147Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:55.147Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:57.734Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:41:57.736Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:00.380Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:00.380Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:02.828Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:02.828Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:05.277Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:05.277Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:07.852Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:07.852Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:10.986Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:10.987Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:13.787Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:13.787Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:16.378Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:16.378Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:17.002Z In(05) vmx DISKLIB-LIB   : numIOs = 1300000 numMergedIOs = 57204 numSplitIOs = 10651
2024-01-20T08:42:18.993Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:18.993Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:21.840Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:21.840Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:24.231Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:24.231Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:26.665Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:26.665Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:29.447Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:29.447Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:31.881Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:31.881Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:34.322Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:34.322Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:36.740Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:36.740Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:40.063Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:40.063Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:41.266Z In(05) vmx DISKLIB-LIB   : numIOs = 1350000 numMergedIOs = 58089 numSplitIOs = 10945
2024-01-20T08:42:42.807Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:42.807Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:45.458Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:45.458Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:48.233Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:48.234Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:51.087Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:51.087Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:53.820Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:53.820Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:56.682Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:56.682Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:59.328Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:42:59.328Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.132Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.132Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.133Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.133Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.136Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.136Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.138Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.139Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.141Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.142Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.145Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:43:19.145Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:44:00.918Z In(05) vmx DISKLIB-LIB   : numIOs = 1400000 numMergedIOs = 62439 numSplitIOs = 11434
2024-01-20T08:44:37.278Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:44:37.278Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.138Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.138Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.140Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.140Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.144Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.145Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.147Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.147Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.151Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.151Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.152Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:45:19.152Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.145Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.145Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.147Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.147Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.150Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.151Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.153Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.153Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.156Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.157Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.158Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:47:19.158Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:42.558Z In(05) vmx Tools: sending 'OS_Halt' (state = 1) state change request
2024-01-20T08:48:42.558Z In(05) vmx Tools: Changing running status: 1 => 2.
2024-01-20T08:48:42.558Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 2259 (last received 0s ago)
2024-01-20T08:48:42.559Z In(05) vmx Vix: [vmxCommands.c:536]: VMAutomation_InitiatePowerOff. Tried to soft halt. Success = 1
2024-01-20T08:48:42.570Z In(05) vcpu-0 Guest: Executing script for state change 'OS_Halt'.
2024-01-20T08:48:42.579Z In(05) vcpu-0 Tools: State change '1' progress: last event 0, event 1, success 1.
2024-01-20T08:48:42.793Z In(05) vcpu-0 Guest: Script exit code: 0, success = 1
2024-01-20T08:48:42.793Z In(05) vcpu-0 TOOLS state change 1 returned status 1
2024-01-20T08:48:42.793Z In(05) vcpu-0 Tools: State change '1' progress: last event 1, event 2, success 1.
2024-01-20T08:48:42.793Z In(05) vcpu-0 Tools: State change '1' progress: last event 1, event 4, success 1.
2024-01-20T08:48:42.793Z In(05) vcpu-0 Tools: Changing running status: 2 => 1.
2024-01-20T08:48:42.793Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 2259 (last received 0s ago)
2024-01-20T08:48:42.794Z In(05) vcpu-0 Guest: Initiating halt.
2024-01-20T08:48:46.021Z In(05) vcpu-0 DnDCP: set guest controllers to version 4
2024-01-20T08:48:46.021Z In(05) vcpu-0 DnDCP: set guest controllers to version 4
2024-01-20T08:48:46.022Z No(00) vcpu-0 ConfigDB: Unsetting "unity.wasCapable"
2024-01-20T08:48:46.469Z In(05) vcpu-0 GuestRpc: Channel 3, unable to send an rpc.
2024-01-20T08:48:46.469Z In(05) vcpu-0 GuestRpc: Reinitializing Channel 3(toolbox-dnd)
2024-01-20T08:48:46.469Z In(05) vcpu-0 TOOLS: appName=toolbox-dnd, oldStatus=1, status=0, guestInitiated=0.
2024-01-20T08:48:47.095Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:47.095Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:47.242Z In(05) vcpu-0 SOUNDLIB: Creating WAVE backend stream.
2024-01-20T08:48:47.249Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (S/PDIF) (High De
2024-01-20T08:48:47.249Z In(05) vcpu-0 SOUNDLIB: SoundWaveSelectHostDevice: Found device Digital Audio (HDMI) (High Defi
2024-01-20T08:48:47.269Z In(05) vcpu-0 SOUNDLIB: SoundWaveComputeBufferParams: Initializing 10 buffers of length 3840, capable of holding a total of 200000 usecs of sound.
2024-01-20T08:48:47.269Z In(05) soundWavePlayback15 VTHREAD 6740 "soundWavePlayback15"
2024-01-20T08:48:47.269Z In(05) soundWavePlayback15 SOUNDLIB: SoundWaveThread: Poll thread entered : id(6740)
2024-01-20T08:48:47.269Z In(05) vcpu-0 SOUNDLIB: SoundWaveCreateStream: Created poll thread : id(6740)
2024-01-20T08:48:47.280Z In(05) vcpu-0 SOUNDLIB: Starting WAVE stream 1AD57572990.
2024-01-20T08:48:49.990Z In(05) vmx SOUNDLIB: Stopping WAVE stream 1AD57572990.
2024-01-20T08:48:50.001Z In(05) soundWavePlayback15 SOUNDLIB: SoundWavePoll: Poll invoked on a non-running stream; leaving early.
2024-01-20T08:48:50.001Z In(05) vmx SOUNDLIB: Closing WAVE stream 1AD57572990.
2024-01-20T08:48:50.001Z In(05) vmx SOUNDLIB: SoundWaveCloseStream: Destroying poll thread : id(6740)
2024-01-20T08:48:50.001Z In(05) soundWavePlayback15 SOUNDLIB: SoundWaveThread: Poll thread exited : id(6740)
2024-01-20T08:48:55.003Z In(05) vcpu-0 TOOLS autoupgrade protocol version 0
2024-01-20T08:48:55.104Z In(05) vcpu-0 GuestRpc: Reinitializing Channel 0(toolbox)
2024-01-20T08:48:55.104Z In(05) vcpu-0 Tools: [AppStatus] Last heartbeat value 2271 (last received 0s ago)
2024-01-20T08:48:55.104Z In(05) vcpu-0 TOOLS: appName=toolbox, oldStatus=1, status=0, guestInitiated=1.
2024-01-20T08:48:55.616Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-01-20T08:48:56.601Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:56.601Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:56.602Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:56.602Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:56.604Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:56.604Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:56.606Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:56.606Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:56.607Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:56.607Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:56.607Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:48:56.608Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-01-20T08:49:01.666Z In(05) vcpu-0 HgfsServerExitSessionInternal: teardown session 1AD58601C70 id 0x173b05dc087
2024-01-20T08:49:02.125Z In(05) vcpu-0 CDROM: Unknown command 0x35.
2024-01-20T08:49:02.125Z In(05) vcpu-0 CDROM sata0:1: CMD 0x35 (SYNC CACHE) FAILED (key 0x5 asc 0x20 ascq 0)
2024-01-20T08:49:02.267Z In(05) vcpu-0 CDROM-IMG:  Ignoring a Unit Start or Stop
2024-01-20T08:49:02.786Z In(05) vcpu-0 UHCI: HCReset
2024-01-20T08:49:02.792Z In(05) vcpu-0 SCSI0: RESET BUS
2024-01-20T08:49:09.320Z In(05) vcpu-0 PIIX4: PM Soft Off.  Good-bye.
2024-01-20T08:49:09.320Z In(05) vcpu-0 Chipset: The guest has requested that the virtual machine be powered off.
2024-01-20T08:49:09.320Z No(00) vcpu-0 ConfigDB: Setting softPowerOff = "TRUE"
2024-01-20T08:49:09.363Z In(05) vcpu-0 VMX: Issuing power-off request...
2024-01-20T08:49:09.363Z In(05) vmx Stopping VCPU threads...
2024-01-20T08:49:09.364Z In(05) vmx MKSThread: Requesting MKS exit
2024-01-20T08:49:09.364Z In(05) vmx Stopping MKS/SVGA threads
2024-01-20T08:49:09.364Z In(05) svga SVGA thread is exiting the main loop
2024-01-20T08:49:09.369Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-01-20T08:49:09.373Z In(05) mks DXGIPresentation: Clearing device context
2024-01-20T08:49:09.373Z In(05) mks DXGIPresentation: Release D3D device
2024-01-20T08:49:09.375Z In(05) mks DXGIPresentation: Releasing DXGI Adapter
2024-01-20T08:49:09.375Z In(05) mks DXGIPresentation: Releasing DXGI factory
2024-01-20T08:49:09.375Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 0.
2024-01-20T08:49:09.375Z In(05) vmx MKS/SVGA threads are stopped
2024-01-20T08:49:09.375Z In(05) vmx USB: Disconnecting device 0x200000050e0f0003
2024-01-20T08:49:09.376Z In(05) vmx 
2024-01-20T08:49:09.376Z In(05)+ vmx OvhdMem: Final (Power Off) Overheads
2024-01-20T08:49:09.376Z In(05) vmx                                                       reserved      |          used
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  524288 524288      - | 522804 524048      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7424   7424      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   15360  15360      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem Total excluded                      :  547072 547072      - |      -      -      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem Actual maximum                      :         547072        |             -
2024-01-20T08:49:09.376Z In(05)+ vmx 
2024-01-20T08:49:09.376Z In(05) vmx                                                       reserved      |          used
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       2      2      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       1      1      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  1048576 1048576      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :    2944   2944      - |    385    385      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :     196    196      - |    196    196      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaShaderTable            :    1000   1000      - |    193    193      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaShaderText             :    2304   2304      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaContextCache           :    1357   1357      - |      2      2      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXRTViewTable          :      31     31      - |      7      7      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXDSViewTable          :      16     16      - |      4      4      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXSRViewTable          :    1021   1021      - |    193    193      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXUAViewTable          :      70     70      - |     13     13      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXBlendStateTable      :      31     31      - |      4      4      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXElementLayoutTable   :     199    199      - |      4      4      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXDepthStencilTable    :      19     19      - |      4      4      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXRasterizerStateTable :      19     19      - |      4      4      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXSamplerTable         :     141    141      - |     13     13      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXContextTable         :    1472   1472      - |      1      1      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderTable          :     241    241      - |     49     49      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderText           :    4096   4096      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXStreamOutTable       :     516    516      - |      1      1      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaDXQueryTable           :     132    132      - |     13     13      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaVAProcessorTable       :       1      1      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaVADecoderTable         :       1      1      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :    1026   1026      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :   10752  10752      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      1     11      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :    3494   3494      - |   3492   3492      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  2547712 2547712      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_VmxMksLLVM                 :   12288  12288      - |      0      0      -
2024-01-20T08:49:09.376Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    2560   2560      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   17664  17664      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem Total paged                         :  3957055 3957055      - |   4589   4599      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem Actual maximum                      :         3957055        |          4599
2024-01-20T08:49:09.377Z In(05)+ vmx 
2024-01-20T08:49:09.377Z In(05) vmx                                                       reserved      |          used
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :     103    103      - |     87     87      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      19     19      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_PFrame                     :    1756   1967      - |   1756   1756      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |     16     16      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |     64     64      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      1      1      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      3      3      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       1      1      - |      1      1      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      8      8      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      2      2      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_AHCIBIOS                   :      16     16      - |      2      2      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |     57     57      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    2048   2048      - |   1280   1280      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      1      1      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      1      1      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      2      2      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      2      2      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       1      1      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       1      1      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      35     35      - |     35     35      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_MonNuma                    :     237    237      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem Total nonpaged                      :    4544   5203      - |   3318   3318      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem Actual maximum                      :           5203        |          3318
2024-01-20T08:49:09.377Z In(05)+ vmx 
2024-01-20T08:49:09.377Z In(05) vmx                                                       reserved      |          used
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_Alloc                       :      97     97      - |     21     74      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :     579    588      - |    579    579      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :       8      8      - |      8      8      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      1      1      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      1      1      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       4      4      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       1      1      - |      1      1      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      24     24      - |     24     24      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_TC                          :     513    513      - |    477    477      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       5      5      - |      5      5      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       6      6      - |      5      5      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       2      2      - |      2      2      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_HV                          :       3      3      - |      3      3      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       2      2      - |      0      0      -
2024-01-20T08:49:09.377Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_VHV                         :       3      3      - |      0      0      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_VNPTShadow                  :    1869   1869      - |      6      6      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_VNPTShadowCache             :      17     17      - |      2      2      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_VNPTBackmap                 :    1033   1179      - |      4      4      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_SVMIDT                      :       1      1      - |      1      1      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_Numa                        :      24     24      - |     14     24      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_NumaTextRodata              :     211    392      - |    181    362      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_NumaDataBss                 :      26     26      - |     26     26      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_NumaLargeData               :       0    512      - |      0      0      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      29     29      - |     22     23      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :       0   1883      - |      0    329      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    1194   1194      - |   1166   1166      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     264    264      - |     73     73      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |     96     96      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       6      6      - |      0      0      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem Total anonymous                     :    6030   8761      - |   2718   3292      -
2024-01-20T08:49:09.378Z In(05) vmx OvhdMem Actual maximum                      :           8068        |          2718
2024-01-20T08:49:09.378Z In(05)+ vmx 
2024-01-20T08:49:09.378Z In(05) vmx VMMEM: Maximum Reservation: 15511MB (MainMem=2048MB)
2024-01-20T08:49:09.378Z In(05) vmx MemSched: BALLOON HIST [0, 524288]: 2342 2342 0 0 0 0 0 0 0 0 0 0
2024-01-20T08:49:09.378Z In(05) vmx MemSched: BALLOON P50 1 P70 1 P90 1 MIN 0 MAX 0
2024-01-20T08:49:09.378Z In(05) vmx MemSched: SWAP HIST [0, 524288]: 98 2342 0 0 0 0 0 0 0 0 0 0
2024-01-20T08:49:09.378Z In(05) vmx MemSched: SWAP P50 10 P70 10 P90 10 MIN 0 MAX 3661
2024-01-20T08:49:09.378Z In(05) vmx MemSched: LOCK HIST [0, 524288]: 0 8 2 1 1 2 2 1 2 1 2322 2320
2024-01-20T08:49:09.378Z In(05) vmx MemSched: LOCK P50 100 P70 100 P90 100 MIN 1984 MAX 530097
2024-01-20T08:49:09.378Z In(05) vmx MemSched: LOCK_TARGET HIST [0, 524288]: 0 0 0 0 0 0 0 0 0 0 2342 2342
2024-01-20T08:49:09.378Z In(05) vmx MemSched: LOCK_TARGET P50 100 P70 100 P90 100 MIN 524288 MAX 537111
2024-01-20T08:49:09.378Z In(05) vmx MemSched: ACTIVE_PCT HIST [0, 100]: 0 0 0 0 326 183 452 245 291 395 450 0
2024-01-20T08:49:09.378Z In(05) vmx MemSched: ACTIVE_PCT P50 70 P70 90 P90 100 MIN 31 MAX 98
2024-01-20T08:49:09.378Z In(05) vmx MemSched: NUM_VMS HIST [0, 10]: 0 0 2342 0 0 0 0 0 0 0 0 0
2024-01-20T08:49:09.378Z In(05) vmx MemSched: NUM_VMS P50 20 P70 20 P90 20 MIN 1 MAX 1
2024-01-20T08:49:09.378Z In(05) vmx MemSched: HOSTLOCK HIST [0, 4120576]: 0 18 2324 0 0 0 0 0 0 0 0 0
2024-01-20T08:49:09.378Z In(05) vmx MemSched: HOSTLOCK P50 20 P70 20 P90 20 MIN 1984 MAX 530097
2024-01-20T08:49:09.378Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '0'
2024-01-20T08:49:09.379Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox' while GuestRpc is powering off.
2024-01-20T08:49:09.379Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox-dnd' while GuestRpc is powering off.
2024-01-20T08:49:09.379Z In(05) vmx TOOLS received request in VMX to set option 'copypaste' -> '0'
2024-01-20T08:49:09.379Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox' while GuestRpc is powering off.
2024-01-20T08:49:09.379Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox-dnd' while GuestRpc is powering off.
2024-01-20T08:49:09.379Z In(05) vmx HgfsChannelBdDisconnectInternal: Backdoor HGFS server session terminated.
2024-01-20T08:49:09.379Z In(05) vmx HgfsChannelBdCloseInternal: Backdoor HGFS server session destroyed.
2024-01-20T08:49:09.379Z In(05) vmx HgfsChannelDeactivateChannel: HGFS [backdoor] channel DEACTIVATED.
2024-01-20T08:49:09.379Z In(05) vmx HgfsChannelDeactivateChannel: HGFS [vmci] channel DEACTIVATED.
2024-01-20T08:49:09.379Z In(05) hgfsOplockThread HgfsOplockIOCPMonitorThread: exit.
2024-01-20T08:49:09.379Z In(05) vmx HgfsServer_ExitState: exit threadpool - inactive.
2024-01-20T08:49:09.379Z In(05) vmx HgfsServerManagerVigorExit: Destroy:
2024-01-20T08:49:09.380Z In(05) vmx Tools: ToolsRunningStatus_Exit, delayedRequest is 0x0
2024-01-20T08:49:09.380Z In(05) vmx Tools: Changing running status: 1 => 0.
2024-01-20T08:49:09.380Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 2273 (last received 6s ago)
2024-01-20T08:49:09.381Z In(05) vmx TOOLS: appName=toolbox-dnd, oldStatus=0, status=0, guestInitiated=0.
2024-01-20T08:49:09.381Z In(05) vmx Tools: [AppStatus] Last heartbeat value 2273 (last received 6s ago)
2024-01-20T08:49:09.381Z In(05) vmx TOOLS: appName=toolbox, oldStatus=0, status=0, guestInitiated=0.
2024-01-20T08:49:09.382Z In(05) vmx SOUNDLIB: Closing Wave sound backend.
2024-01-20T08:49:09.382Z In(05) vmx USB: Disconnecting device 0x10e0f0002
2024-01-20T08:49:09.389Z In(05) usbCCIDEnumCards USB-CCID: Card enum thread exiting.
2024-01-20T08:49:09.392Z In(05) vmx SOUNDLIB: Closing Wave sound backend.
2024-01-20T08:49:09.394Z In(05) mks MKSControlMgr: disconnected
2024-01-20T08:49:09.443Z In(05) mks MKS-RenderMain: Stopped ISBRenderer (DX11Renderer)
2024-01-20T08:49:09.444Z In(05) mks MKS PowerOff
2024-01-20T08:49:09.444Z In(05) svga SVGA thread is exiting
2024-01-20T08:49:09.444Z In(05) mks MKS thread is exiting
2024-01-20T08:49:09.445Z Wa(03) vmx 
2024-01-20T08:49:09.446Z In(05) vmx scsi0:0: numIOs = 1444264 numMergedIOs = 67596 numSplitIOs = 12302 (15.4%)
2024-01-20T08:49:09.446Z In(05) vmx Closing disk 'scsi0:0'
2024-01-20T08:49:09.501Z In(05) vmx AIOWIN32C: asyncOps=1500286 syncOps=308 bufSize=288Kb fixedOps=268781
2024-01-20T08:49:09.501Z In(05) aioCompletion AIO thread processed 1500286 completions
2024-01-20T08:49:09.738Z In(05) deviceThread Device thread is exiting
2024-01-20T08:49:09.739Z In(05) vmx Vix: [mainDispatch.c:1164]: VMAutomationPowerOff: Powering off.
2024-01-20T08:49:09.740Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("F:\vm_lonele\Windows 7 的克隆.vmpl", ...) failed, error: 2
2024-01-20T08:49:09.740Z In(05) vmx Policy_SavePolicyFile: invalid arguments to function.
2024-01-20T08:49:09.740Z In(05) vmx PolicyVMX_Exit: Could not write out policies: 15.
2024-01-20T08:49:09.740Z In(05) vmx WORKER: asyncOps=3 maxActiveOps=1 maxPending=1 maxCompleted=1
2024-01-20T08:49:09.741Z In(05) PowerNotifyThread PowerNotify thread exiting.
2024-01-20T08:49:10.083Z In(05) vmx Vix: [mainDispatch.c:4206]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-01-20T08:49:10.083Z In(05) vmx Vix: [mainDispatch.c:4224]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2024-01-20T08:49:10.084Z In(05) vmx VMXSTATS: Ready to cleanup and munmap 1AD569D0000.
2024-01-20T08:49:10.085Z No(00) vmx ConfigDB: Setting cleanShutdown = "TRUE"
2024-01-20T08:49:10.119Z In(05) vmx Vix: [mainDispatch.c:4206]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2024-01-20T08:49:10.119Z In(05) vmx Vix: [mainDispatch.c:4224]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2024-01-20T08:49:10.119Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2024-01-20T08:49:10.119Z In(05) vmx VMX idle exit
2024-01-20T08:49:10.119Z In(05) vmx WQPoolFreePoll : pollIx = 3, signalHandle = 2568
2024-01-20T08:49:10.121Z In(05) vmx Vix: [mainDispatch.c:815]: VMAutomation_LateShutdown()
2024-01-20T08:49:10.121Z In(05) vmx Vix: [mainDispatch.c:770]: VMAutomationCloseListenerSocket. Closing listener socket.
2024-01-20T08:49:10.121Z In(05) vmx Flushing VMX VMDB connections
2024-01-20T08:49:10.121Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2024-01-20T08:49:10.121Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (0)
2024-01-20T08:49:10.121Z In(05) vmx VigorTransport_ServerDestroy: server destroyed.
2024-01-20T08:49:10.121Z In(05) vmx WQPoolFreePoll : pollIx = 2, signalHandle = 3044
2024-01-20T08:49:10.121Z In(05) vmx WQPoolFreePoll : pollIx = 1, signalHandle = 724
2024-01-20T08:49:10.131Z In(05) vmx VMX exit (0).
2024-01-20T08:49:10.141Z In(05) vmx OBJLIB-LIB: ObjLib cleanup done.
2024-01-20T08:49:10.141Z In(05) vmx AIOMGR-S : stat o=5 r=27 w=2 i=0 br=205296 bw=12
