#!/usr/bin/env python3
"""
VMDK文件诊断工具
检查VMDK文件的完整性和可访问性
"""

import os
import sys

def check_vmdk_accessibility(vmdk_path):
    """检查VMDK文件的可访问性"""
    print(f"诊断VMDK文件: {vmdk_path}")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists(vmdk_path):
        print("❌ 文件不存在")
        return False
    
    print("✅ 文件存在")
    
    # 检查文件权限
    if not os.access(vmdk_path, os.R_OK):
        print("❌ 文件不可读")
        return False
    
    print("✅ 文件可读")
    
    # 检查文件大小
    file_size = os.path.getsize(vmdk_path)
    print(f"📏 文件大小: {file_size} 字节")
    
    # 检查是否为描述符文件
    try:
        with open(vmdk_path, 'r', encoding='utf-8', errors='ignore') as f:
            first_line = f.readline().strip()
            if first_line.startswith('# Disk DescriptorFile'):
                print("✅ 这是一个VMDK描述符文件")
                
                # 读取并检查extent文件
                f.seek(0)
                extents = []
                for line in f:
                    line = line.strip()
                    if line.startswith('RW ') and 'SPARSE' in line:
                        quote_start = line.find('"')
                        quote_end = line.rfind('"')
                        if quote_start != -1 and quote_end != -1:
                            extent_file = line[quote_start+1:quote_end]
                            extents.append(extent_file)
                
                print(f"📋 描述符定义了 {len(extents)} 个extent文件")
                
                # 检查extent文件
                existing_extents = 0
                missing_extents = 0
                
                for extent in extents:
                    extent_path = os.path.join(os.path.dirname(vmdk_path), extent)
                    if os.path.exists(extent_path):
                        existing_extents += 1
                    else:
                        missing_extents += 1
                
                print(f"✅ 存在的extent文件: {existing_extents}")
                print(f"💡 缺失的extent文件: {missing_extents} (稀疏格式正常)")
                
                if existing_extents > 0:
                    print("🎯 结论: VMDK文件应该可以正常使用")
                    return True
                else:
                    print("❌ 结论: 没有找到任何extent文件，可能有问题")
                    return False
                    
            else:
                print("❌ 这不是一个有效的VMDK描述符文件")
                return False
                
    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")
        return False

def check_vmware_compatibility():
    """检查VMware兼容性"""
    print("\nVMware兼容性检查:")
    print("-" * 30)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 检查路径中是否有特殊字符
    if any(ord(c) > 127 for c in current_dir):
        print("⚠️  路径包含非ASCII字符，可能导致VMware兼容性问题")
        print("💡 建议: 将文件移动到纯英文路径")
    else:
        print("✅ 路径兼容性良好")
    
    # 检查空格
    if ' ' in current_dir:
        print("⚠️  路径包含空格，某些VMware版本可能有问题")
        print("💡 建议: 在VMX文件中使用绝对路径")
    
    # 检查文件系统
    import platform
    if platform.system() == 'Linux':
        print("🐧 检测到Linux系统")
        print("💡 建议: 确保VMware Workstation Pro支持当前文件系统")

def main():
    if len(sys.argv) != 2:
        print("用法: python3 vmdk_diagnostic.py <vmdk文件路径>")
        print("示例: python3 vmdk_diagnostic.py 'Windows 7-cl1-000002.vmdk'")
        return
    
    vmdk_path = sys.argv[1]
    
    # 检查VMDK文件
    vmdk_ok = check_vmdk_accessibility(vmdk_path)
    
    # 检查VMware兼容性
    check_vmware_compatibility()
    
    print("\n" + "=" * 50)
    if vmdk_ok:
        print("🎉 诊断完成: VMDK文件看起来正常")
        print("\n建议的解决方案:")
        print("1. 使用绝对路径在VMX文件中指定VMDK文件")
        print("2. 确保VMware有足够的权限访问文件")
        print("3. 尝试使用简化的VMX配置")
    else:
        print("❌ 诊断完成: 发现问题")
        print("\n建议的解决方案:")
        print("1. 检查文件完整性")
        print("2. 尝试使用其他快照")
        print("3. 从备份恢复文件")

if __name__ == "__main__":
    main()
