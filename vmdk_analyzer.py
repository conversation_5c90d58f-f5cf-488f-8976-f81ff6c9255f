#!/usr/bin/env python3
"""
VMDK文件分析工具
用于深入分析VMware虚拟磁盘文件的二进制结构和内容
"""

import struct
import os
import sys

def analyze_vmdk_header(filename):
    """分析VMDK文件头部信息"""
    print(f'\n=== VMDK头部分析: {filename} ===')
    
    if not os.path.exists(filename):
        print(f'文件不存在: {filename}')
        return None
        
    with open(filename, 'rb') as f:
        # 读取VMDK头部
        header = f.read(512)
        
        if len(header) < 512:
            print(f'文件太小，只有 {len(header)} 字节')
            return None
            
        # 检查魔数
        magic = header[0:4]
        if magic != b'KDMV':
            print(f'无效的VMDK文件，魔数: {magic}')
            return None
            
        # 解析版本
        version = struct.unpack('<I', header[4:8])[0]
        print(f'魔数: {magic.decode("ascii")}')
        print(f'版本: {version}')
        
        # 解析标志
        flags = struct.unpack('<I', header[8:12])[0]
        print(f'标志: 0x{flags:08x}')
        print(f'  - 稀疏格式: {bool(flags & 0x1)}')
        print(f'  - 压缩: {bool(flags & 0x2)}')
        print(f'  - 标记: {bool(flags & 0x4)}')
        
        # 解析容量（扇区数）
        capacity = struct.unpack('<Q', header[12:20])[0]
        print(f'容量(扇区): {capacity:,}')
        print(f'容量(MB): {capacity * 512 / 1024 / 1024:.2f}')
        
        # 解析粒度大小
        grain_size = struct.unpack('<Q', header[20:28])[0]
        print(f'粒度大小: {grain_size}')
        
        # 解析描述符偏移和大小
        desc_offset = struct.unpack('<Q', header[28:36])[0]
        desc_size = struct.unpack('<Q', header[36:44])[0]
        print(f'描述符偏移: {desc_offset}')
        print(f'描述符大小: {desc_size}')
        
        # 解析粒度表信息
        num_gtes_per_gt = struct.unpack('<I', header[44:48])[0]
        rgd_offset = struct.unpack('<Q', header[48:56])[0]
        gd_offset = struct.unpack('<Q', header[56:64])[0]
        overhead = struct.unpack('<Q', header[64:72])[0]
        
        print(f'每个GT的GTE数量: {num_gtes_per_gt}')
        print(f'冗余粒度目录偏移: {rgd_offset}')
        print(f'粒度目录偏移: {gd_offset}')
        print(f'开销扇区: {overhead}')
        
        # 解析其他标志
        unclean_shutdown = header[72]
        compression_algorithm = struct.unpack('<H', header[77:79])[0]
        print(f'非正常关闭标志: {unclean_shutdown}')
        print(f'压缩算法: {compression_algorithm}')
        
        return {
            'capacity': capacity,
            'grain_size': grain_size,
            'gd_offset': gd_offset,
            'num_gtes_per_gt': num_gtes_per_gt,
            'flags': flags
        }

def analyze_grain_table_usage(filename):
    """分析粒度表使用情况"""
    print(f'\n=== 粒度表使用情况: {filename} ===')
    
    with open(filename, 'rb') as f:
        # 读取头部获取关键信息
        header = f.read(512)
        gd_offset = struct.unpack('<Q', header[56:64])[0]
        capacity = struct.unpack('<Q', header[12:20])[0]
        grain_size = struct.unpack('<Q', header[20:28])[0]
        
        # 计算粒度表参数
        grains_per_gt = 512  # 每个粒度表512个条目
        total_grains = capacity // grain_size
        num_gts = (total_grains + grains_per_gt - 1) // grains_per_gt
        
        print(f'总粒度数: {total_grains:,}')
        print(f'需要的粒度表数: {num_gts}')
        
        # 读取粒度目录
        f.seek(gd_offset * 512)
        gd_data = f.read(num_gts * 4)
        
        allocated_grains = 0
        used_gts = 0
        
        # 遍历所有粒度表
        for i in range(num_gts):
            gt_offset = struct.unpack('<I', gd_data[i*4:(i+1)*4])[0]
            if gt_offset != 0:
                used_gts += 1
                # 读取粒度表
                f.seek(gt_offset * 512)
                gt_data = f.read(grains_per_gt * 4)
                
                # 统计已分配的粒度
                for j in range(grains_per_gt):
                    if j * 4 + 4 <= len(gt_data):
                        grain_offset = struct.unpack('<I', gt_data[j*4:(j+1)*4])[0]
                        if grain_offset != 0:
                            allocated_grains += 1
        
        print(f'使用的粒度表: {used_gts}/{num_gts}')
        print(f'已分配的粒度: {allocated_grains:,}/{total_grains:,}')
        print(f'数据分配率: {allocated_grains/total_grains*100:.1f}%')
        
        # 计算文件大小和稀疏度
        f.seek(0, 2)
        file_size = f.tell()
        print(f'文件大小: {file_size:,} 字节 ({file_size / 1024 / 1024:.2f} MB)')
        
        if capacity > 0:
            sparsity = (1 - file_size / (capacity * 512)) * 100
            print(f'稀疏度: {sparsity:.1f}%')
        
        return {
            'allocated_grains': allocated_grains,
            'total_grains': total_grains,
            'allocation_rate': allocated_grains/total_grains*100,
            'file_size': file_size,
            'sparsity': sparsity if capacity > 0 else 0
        }

def check_filesystem_signature(filename):
    """检查文件系统签名和数据内容"""
    print(f'\n=== 文件系统签名检查: {filename} ===')
    
    with open(filename, 'rb') as f:
        # 读取头部信息
        header = f.read(512)
        gd_offset = struct.unpack('<Q', header[56:64])[0]
        
        # 读取粒度目录
        f.seek(gd_offset * 512)
        gd_data = f.read(4096)
        
        # 查找第一个有效的数据块
        for i in range(min(1024, len(gd_data) // 4)):
            gt_offset = struct.unpack('<I', gd_data[i*4:(i+1)*4])[0]
            if gt_offset != 0:
                # 读取粒度表
                f.seek(gt_offset * 512)
                gt_data = f.read(2048)
                
                # 找到第一个有效的粒度
                for j in range(min(512, len(gt_data) // 4)):
                    grain_offset = struct.unpack('<I', gt_data[j*4:(j+1)*4])[0]
                    if grain_offset != 0:
                        # 读取实际数据
                        f.seek(grain_offset * 512)
                        data = f.read(512)
                        
                        print(f'第一个数据块位置: GT[{i}], Grain[{j}]')
                        print(f'物理偏移: {grain_offset * 512:,} (0x{grain_offset * 512:x})')
                        
                        # 检查文件系统签名
                        signatures = []
                        if len(data) >= 512 and data[510:512] == b'\x55\xaa':
                            signatures.append('MBR引导记录 (0x55AA)')
                        
                        if len(data) >= 11 and data[3:11] == b'NTFS    ':
                            signatures.append('NTFS文件系统')
                        elif len(data) >= 62 and data[54:62] == b'FAT32   ':
                            signatures.append('FAT32文件系统')
                        elif len(data) >= 87 and data[82:87] == b'FAT16':
                            signatures.append('FAT16文件系统')
                        
                        if signatures:
                            print('发现文件系统签名:')
                            for sig in signatures:
                                print(f'  - {sig}')
                        else:
                            print('未发现标准文件系统签名')
                        
                        # 显示数据内容
                        print('数据内容 (前64字节):')
                        for k in range(0, min(64, len(data)), 16):
                            hex_part = ' '.join(f'{b:02x}' for b in data[k:k+16])
                            ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[k:k+16])
                            print(f'{k:04x}: {hex_part:<48} |{ascii_part}|')
                        
                        return True
                        
        print('未找到有效的数据块')
        return False

def analyze_vmdk_file(filename):
    """完整分析单个VMDK文件"""
    print(f'\n{"="*60}')
    print(f'完整分析: {filename}')
    print(f'{"="*60}')
    
    # 检查文件是否存在
    if not os.path.exists(filename):
        print(f'错误: 文件不存在 - {filename}')
        return
    
    # 分析头部
    header_info = analyze_vmdk_header(filename)
    if not header_info:
        return
    
    # 分析粒度表使用情况
    usage_info = analyze_grain_table_usage(filename)
    
    # 检查文件系统签名
    check_filesystem_signature(filename)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python vmdk_analyzer.py <vmdk文件1> [vmdk文件2] ...")
        print("示例: python vmdk_analyzer.py 'Windows 7-cl1.vmdk'")
        return
    
    # 分析所有指定的文件
    for filename in sys.argv[1:]:
        analyze_vmdk_file(filename)

if __name__ == "__main__":
    main()
