@echo off
chcp 65001 >nul
echo VMDK文件选择器
echo ================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.6或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查脚本文件是否存在
if not exist "vmdk_selector.py" (
    echo 错误: 未找到vmdk_selector.py文件
    echo 请确保该文件与此批处理文件在同一目录中
    pause
    exit /b 1
)

REM 运行Python脚本
echo 正在分析VMDK文件...
echo.
python vmdk_selector.py

echo.
echo 分析完成！
echo.
pause
