VMDK分析工具包
===============

已保存的文件列表：

1. 核心分析工具
   ├── vmdk_analyzer.py      - 详细的VMDK二进制结构分析工具
   └── vmdk_selector.py      - 快速VMDK文件选择推荐工具

2. 启动脚本
   ├── run_vmdk_selector.bat - Windows批处理启动脚本
   └── run_vmdk_selector.sh  - Linux/Mac Shell启动脚本

3. 配置文件
   └── Windows 7 的克隆.vmx  - 示例VMX虚拟机配置文件

4. 文档
   └── README_VMDK_Tools.md  - 详细使用说明文档

使用方法：
=========

快速使用（推荐）：
- Windows: 双击 run_vmdk_selector.bat
- Linux/Mac: ./run_vmdk_selector.sh
- 或直接运行: python3 vmdk_selector.py

详细分析：
python3 vmdk_analyzer.py "文件名.vmdk"

主要功能：
=========

vmdk_selector.py:
- 自动识别VMDK文件链和快照结构
- 推荐最佳的VMDK文件选择
- 检查文件完整性
- 生成VMX配置建议

vmdk_analyzer.py:
- 深入分析VMDK二进制结构
- 解析文件头部信息
- 统计数据分配率和稀疏度
- 检测文件系统类型
- 分析粒度表使用情况

当前分析结果：
=============

推荐使用: Windows 7-cl1-000002.vmdk
原因: 这是最新的快照（2024-01-20），包含最完整的虚拟机状态

VMX配置:
scsi0:0.fileName = "Windows 7-cl1-000002.vmdk"
scsi0:0.present = "TRUE"
scsi0:0.deviceType = "scsi-hardDisk"

注意事项：
- 部分数据文件缺失，但这是正常的（快照的稀疏特性）
- 确保保持完整的快照链
- 建议备份整个虚拟机目录

技术细节：
=========

快照链结构:
Windows 7-cl1.vmdk (基础磁盘, 2023-12-29)
    ↓
Windows 7-cl1-000001.vmdk (快照1, 2024-01-08)
    ↓
Windows 7-cl1-000002.vmdk (快照2, 2024-01-20) ← 推荐

数据分配情况:
- 基础磁盘: 99.5% 分配率 (4045MB)
- 快照1: 52.6% 分配率 (2140MB)  
- 快照2: 21.8% 分配率 (888MB)

文件系统检测:
- 所有快照都包含有效的NTFS文件系统
- 包含MBR引导记录
- 可以正常启动
