2024-01-20T08:10:04.856Z In(05) mks Log for VMware Workstation pid=8912 version=16.2.1 build=build-18811642 option=Release
2024-01-20T08:10:04.856Z In(05) mks The host is 64-bit.
2024-01-20T08:10:04.856Z In(05) mks Host codepage=windows-936 encoding=GBK
2024-01-20T08:10:04.856Z In(05) mks Host is Windows 10 Pro, 64-bit (Build 22000.1042)
2024-01-20T08:10:04.856Z In(05) mks Host offset from UTC is -08:00.
2024-01-20T08:10:07.146Z In(05) main  VTHREAD 2348 "main"
2024-01-20T08:10:07.146Z In(05) main  MKSRoleMain: Initializing MKSSandbox...
2024-01-20T08:10:07.147Z In(05) main  MKSRoleMain: Initializing FeatureState...
2024-01-20T08:10:07.147Z In(05) main  MKSRoleMain: Initializing Locale...
2024-01-20T08:10:07.147Z In(05) main  LOCALE GBK -> zh_CN User=804 System=804
2024-01-20T08:10:07.147Z In(05) main  Msg_SetLocaleEx: HostLocale=GBK UserLocale=zh_CN
2024-01-20T08:10:07.147Z In(05) main  DictionaryLoad: Cannot open file "C:\Program Files (x86)\VMware\VMware Workstation\\messages\zh_CN\vmware-mks.vmsg": The system cannot find the file specified.
2024-01-20T08:10:07.147Z In(05) main  [msg.dictionary.load.openFailed] Cannot open file "C:\Program Files (x86)\VMware\VMware Workstation\\messages\zh_CN\vmware-mks.vmsg": The system cannot find the file specified.
2024-01-20T08:10:07.148Z Wa(03) main  Cannot load message dictionary "C:\Program Files (x86)\VMware\VMware Workstation\\messages\zh_CN\vmware-mks.vmsg".
2024-01-20T08:10:07.148Z In(05) main  MKSRoleMain: Initializing Preference...
2024-01-20T08:10:07.159Z In(05) main  DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-01-20T08:10:07.159Z In(05) main  [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-01-20T08:10:07.159Z In(05) main  PREF Optional preferences file not found at C:\Users\<USER>\AppData\Roaming\VMware\config.ini. Using default values.
2024-01-20T08:10:07.159Z In(05) main  UUID: SMBIOS UUID is reported as '40 1e 70 61 c7 fe d5 11-80 68 48 5b 39 ca a5 a7'.
2024-01-20T08:10:07.166Z In(05) main  MKSRoleMain: Initializing Poll...
2024-01-20T08:10:07.166Z In(05) main  POLL Using the WSAPoll API Implementation for PollDefault
2024-01-20T08:10:07.166Z In(05) main  MKSRoleMain: Initializing Log...
2024-01-20T08:10:07.168Z In(05) main  MKSRoleMain: Found Config Settings:
2024-01-20T08:10:07.168Z In(05) main  DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini
2024-01-20T08:10:07.168Z In(05) main  DICT          printers.enabled = "FALSE"
2024-01-20T08:10:07.168Z In(05) main  DICT --- NON PERSISTENT (null)
2024-01-20T08:10:07.168Z In(05) main  DICT --- USER PREFERENCES C:\Users\<USER>\AppData\Roaming\VMware\preferences.ini
2024-01-20T08:10:07.168Z In(05) main  DICT  pref.sharedFolder.maxNum = "42"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder0.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder0.guestName = "CH"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder0.hostPath = "D:\cdesktop\SMOS-II Setup For Win7 64(1)\SMOS-II Setup For Win7 64\SMOS-II Server Setup\SetupStep[7]_SMOS_II_Server_Setup\CH"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder0.enabled = "TRUE"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder1.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder1.guestName = "Downloads"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder1.hostPath = "C:\Users\<USER>\Downloads"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder1.enabled = "TRUE"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder2.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder2.guestName = "H"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder2.hostPath = "H:\"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder2.enabled = "FALSE"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder3.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder3.guestName = "SMOS-II-CH V3.2"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder3.hostPath = "D:\u盘优盘\SMOS-II-CH V3.2"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.sharedFolder3.enabled = "TRUE"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.ws.session.window.count = "1"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.ws.session.window0.tab.count = "4"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.ws.session.window0.tab0.cnxType = "vmdb"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.ws.session.window0.tab1.cnxType = "vmdb"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.ws.session.window0.sidebar = "TRUE"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.ws.session.window0.sidebar.width = "200"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.ws.session.window0.statusBar = "TRUE"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.ws.session.window0.tabs = "TRUE"
2024-01-20T08:10:07.168Z In(05) main  DICT pref.ws.session.window0.thumbnailBar = "FALSE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.ws.session.window0.thumbnailBar.size = "100"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.ws.session.window0.thumbnailBar.view = "opened-vms"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.ws.session.window0.placement.left = "0"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.ws.session.window0.placement.top = "704"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.ws.session.window0.placement.right = "1300"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.ws.session.window0.placement.bottom = "1748"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.ws.session.window0.maximized = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT         vmWizard.guestKey = "windows7"
2024-01-20T08:10:07.169Z In(05) main  DICT vmWizard.installMediaType = "later"
2024-01-20T08:10:07.169Z In(05) main  DICT vmWizard.isoLocationMRU.count = "8"
2024-01-20T08:10:07.169Z In(05) main  DICT vmWizard.isoLocationMRU0.location = "C:\Users\<USER>\Videos\Win10_22H2_Chinese_Simplified_x32v1.iso"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.ws.session.window0.tab2.cnxType = "vmdb"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder4.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder4.guestName = "cdesktop"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder4.hostPath = "D:\cdesktop"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder4.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder5.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder5.guestName = "ff"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder5.hostPath = "D:\u盘优盘"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder5.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder6.vmPath = "/vm/#0267290113d702d0/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder6.guestName = "软件"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder6.hostPath = "I:\软件"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder6.enabled = "FALSE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.snapshotManager.left = "74"
2024-01-20T08:10:07.169Z In(05) main  DICT  pref.snapshotManager.top = "474"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.snapshotManager.right = "828"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.snapshotManager.bottom = "1027"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.ws.session.window0.tab3.cnxType = "vmdb"
2024-01-20T08:10:07.169Z In(05) main  DICT vmWizard.isoLocationMRU1.location = "I:\W81PEAV\W81PEAV.iso"
2024-01-20T08:10:07.169Z In(05) main  DICT vmWizard.isoLocationMRU2.location = "I:\[neubt]office2003卸载修复\干净PE系统合辑\无垠PE\无垠Win8PE64维护版V20181218.iso"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder7.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder7.guestName = "Downloads"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder7.hostPath = "D:\Users\bfit\Downloads"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder7.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder8.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder8.guestName = "F"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder8.hostPath = "F:\"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder8.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder9.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder9.guestName = "H"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder9.hostPath = "H:\"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder9.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder10.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder10.guestName = "SMOS-II-CH V3.2"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder10.hostPath = "D:\cdesktop\SMOS-II-CH V3.2"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder10.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder11.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder11.guestName = "chromeDownload"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder11.hostPath = "I:\chromeDownload"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder11.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder12.vmPath = "/vm/#33674cd53eb9b80f/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder12.guestName = "软件"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder12.hostPath = "I:\软件"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder12.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder13.vmPath = "/vm/#46a795c643847c12/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder13.guestName = "Framework462"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder13.hostPath = "C:\Users\<USER>\Downloads\Framework462"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder13.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT vmWizard.isoLocationMRU3.location = "F:\Win10_22H2_Chinese_Simplified_x64v1.iso"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.ws.session.window0.tab4.cnxType = "vmdb"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder14.vmPath = "/vm/#46a795c643847c12/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder14.guestName = "u盘优盘"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder14.hostPath = "D:\u盘优盘"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder14.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder15.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder15.guestName = "Framework462"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder15.hostPath = "C:\Users\<USER>\Downloads\Framework462"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder15.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder16.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder16.guestName = "H"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder16.hostPath = "H:\"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder16.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder17.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder17.guestName = "HRSword5.0.1.1 (1)"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder17.hostPath = "C:\Users\<USER>\Downloads\HRSword5.0.1.1 (1)"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder17.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder18.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder18.guestName = "Sysdiag"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder18.hostPath = "C:\Users\<USER>\Downloads\HRSword2021.01.25\HRSword"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder18.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder19.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder19.guestName = "Sysdiag"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder19.hostPath = "D:\software\共享\Sysdiag"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder19.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder20.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder20.guestName = "SysinternalsSuite"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder20.hostPath = "I:\chromeDownload\SysinternalsSuite"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder20.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder21.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder21.guestName = "cdesktop"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder21.hostPath = "D:\cdesktop"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder21.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT vmWizard.isoLocationMRU4.location = "D:\amd_m2-xplus\Windows XP SP3\windows_xp_professional_with_service_pack_3.iso"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder22.vmPath = "/vm/#4a0a44f40e4753f1/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder22.guestName = "chromeDownload"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder22.hostPath = "I:\chromeDownload"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder22.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder23.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder23.guestName = "Framework462"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder23.hostPath = "C:\Users\<USER>\Downloads\Framework462"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder23.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder24.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder24.guestName = "SMOS-II-CH V3.0"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder24.hostPath = "D:\SMOS-II-CH V3.0-三菱电梯\SMOS-II-CH V3.0"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder24.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder25.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder25.guestName = "SMOS-II-CH V3.0"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder25.hostPath = "D:\SMOS-II-CH V3.0-涓夎彵鐢垫\SMOS-II-CH V3.0"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder25.enabled = "FALSE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder26.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder26.guestName = "SMOS-II-CH V3.2"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder26.hostPath = "D:\cdesktop\SMOS-II-CH V3.2"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder26.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder27.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder27.guestName = "VirtualKD-Redux-2020.0"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder27.hostPath = "C:\Users\<USER>\Downloads\VirtualKD-Redux-2020.0"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder27.enabled = "TRUE"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder28.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder28.guestName = "u盘优盘"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder28.hostPath = "D:\u盘优盘"
2024-01-20T08:10:07.169Z In(05) main  DICT pref.sharedFolder28.enabled = "TRUE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder29.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder29.guestName = "电梯综合监控系统"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder29.hostPath = "C:\Program Files (x86)\上海三菱电梯有限公司\电梯综合监控系统"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder29.enabled = "TRUE"
2024-01-20T08:10:07.170Z In(05) main  DICT vmWizard.isoLocationMRU5.location = "H:\VMware\旧经典版\V11.1.0\tools\windows.iso"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder30.vmPath = "/vm/#906399599004a4ba/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder30.guestName = "鐢垫缁煎悎鐩戞帶绯荤粺"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder30.hostPath = "C:\Program Files (x86)\涓婃捣涓夎彵鐢垫鏈夐檺鍏徃\鐢垫缁煎悎鐩戞帶绯荤粺"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder30.enabled = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder31.vmPath = "/vm/#a1208ea096a895f7/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder31.guestName = "Downloads"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder31.hostPath = "D:\chromeDownload\JDownloaderPortable\Data\Downloads"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder31.enabled = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder32.vmPath = "/vm/#a1208ea096a895f7/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder32.guestName = "Vbsedit5.2.4"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder32.hostPath = "C:\Vbsedit5.2.4"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder32.enabled = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder33.vmPath = "/vm/#a1208ea096a895f7/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder33.guestName = "awms-duibi"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder33.hostPath = "D:\awms-duibi"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder33.enabled = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab5.cnxType = "vmdb"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder34.vmPath = "/vm/#a1208ea096a895f7/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder34.guestName = "考勤demo"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder34.hostPath = "I:\考勤demo"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder34.enabled = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT vmWizard.isoLocationMRU6.location = "\\*************\历史资料库\10、常用软件\03、系统软件\win7\win7_sp1_.iso"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab6.cnxType = "vmdb"
2024-01-20T08:10:07.170Z In(05) main  DICT vmWizard.isoLocationMRU7.location = "D:\u盘优盘\cn_windows_7_professional_with_sp1_vl_build_x64_dvd_u_677816_Driver.iso"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder35.vmPath = "/vm/#d04ddfc72e8e7fb9/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder35.guestName = "Framework462"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder35.hostPath = "C:\Users\<USER>\Downloads\Framework462"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder35.enabled = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder36.vmPath = "/vm/#d04ddfc72e8e7fb9/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder36.guestName = "SMOS-II-CH V3.0"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder36.hostPath = "D:\SMOS-II-CH V3.0-涓夎彵鐢垫\SMOS-II-CH V3.0"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder36.enabled = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder37.vmPath = "/vm/#d04ddfc72e8e7fb9/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder37.guestName = "SMOS-II-CH V3.2"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder37.hostPath = "D:\cdesktop\SMOS-II-CH V3.2"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder37.enabled = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder38.vmPath = "/vm/#d04ddfc72e8e7fb9/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder38.guestName = "VirtualKD-Redux-2020.0"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder38.hostPath = "C:\Users\<USER>\Downloads\VirtualKD-Redux-2020.0"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder38.enabled = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder39.vmPath = "/vm/#d04ddfc72e8e7fb9/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder39.guestName = "鐢垫缁煎悎鐩戞帶绯荤粺"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder39.hostPath = "C:\Program Files (x86)\涓婃捣涓夎彵鐢垫鏈夐檺鍏徃\鐢垫缁煎悎鐩戞帶绯荤粺"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder39.enabled = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder40.vmPath = "/vm/#e03ebf42476068a7/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder40.guestName = "H"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder40.hostPath = "H:\"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder40.enabled = "TRUE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab0.dest = ""
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab0.file = ""
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab0.type = "home"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab0.focused = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab1.dest = ""
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab1.file = "F:\vm_lonele\Windows 7 的克隆.vmx"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab1.type = "vm"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab1.focused = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder41.vmPath = "/vm/#e03ebf42476068a7/"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder41.guestName = "软件"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder41.hostPath = "I:\软件"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.sharedFolder41.enabled = "TRUE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab2.dest = ""
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab2.file = "F:\bf_lonele\Windows 7.vmx"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab2.type = "vm"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab2.focused = "FALSE"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab3.dest = ""
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab3.file = "I:\vmware-awms\Windows Server 2008 R2 x64.vmx"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab3.type = "vm"
2024-01-20T08:10:07.170Z In(05) main  DICT pref.ws.session.window0.tab3.focused = "TRUE"
2024-01-20T08:10:07.170Z In(05) main  DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2024-01-20T08:10:07.170Z In(05) main  DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2024-01-20T08:10:07.170Z In(05) main  DICT         authd.client.port = "902"
2024-01-20T08:10:07.170Z In(05) main  DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-01-20T08:10:07.170Z In(05) main  DICT installerDefaults.autoSoftwareUpdateEnabled = "no"
2024-01-20T08:10:07.170Z In(05) main  DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "30879"
2024-01-20T08:10:07.170Z In(05) main  DICT installerDefaults.componentDownloadEnabled = "yes"
2024-01-20T08:10:07.170Z In(05) main  DICT installerDefaults.dataCollectionEnabled = "no"
2024-01-20T08:10:07.170Z In(05) main  DICT installerDefaults.dataCollectionEnabled.epoch = "30879"
2024-01-20T08:10:07.170Z In(05) main  DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2024-01-20T08:10:07.170Z In(05) main  DICT         authd.client.port = "902"
2024-01-20T08:10:07.170Z In(05) main  DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-01-20T08:10:07.170Z In(05) main  DICT installerDefaults.autoSoftwareUpdateEnabled = "no"
2024-01-20T08:10:07.170Z In(05) main  DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "30879"
2024-01-20T08:10:07.170Z In(05) main  DICT installerDefaults.componentDownloadEnabled = "yes"
2024-01-20T08:10:07.170Z In(05) main  DICT installerDefaults.dataCollectionEnabled = "no"
2024-01-20T08:10:07.170Z In(05) main  DICT installerDefaults.dataCollectionEnabled.epoch = "30879"
2024-01-20T08:10:07.170Z In(05) main  LOCALE GBK -> zh_CN User=804 System=804
2024-01-20T08:10:07.170Z In(05) main  Msg_SetLocaleEx: HostLocale=GBK UserLocale=zh_CN
2024-01-20T08:10:07.170Z In(05) main  DictionaryLoad: Cannot open file "C:\Program Files (x86)\VMware\VMware Workstation\x64\messages\zh_CN\vmware.vmsg": The system cannot find the path specified.
2024-01-20T08:10:07.170Z In(05) main  [msg.dictionary.load.openFailed] Cannot open file "C:\Program Files (x86)\VMware\VMware Workstation\x64\messages\zh_CN\vmware.vmsg": The system cannot find the path specified.
2024-01-20T08:10:07.171Z Wa(03) main  Cannot load message dictionary "C:\Program Files (x86)\VMware\VMware Workstation\x64\messages\zh_CN\vmware.vmsg".
2024-01-20T08:10:07.171Z In(05) main  MKSRoleMain: Initializing Sig
2024-01-20T08:10:07.171Z In(05) main  MKSRoleMain: Initializing MKS
2024-01-20T08:10:07.196Z In(05) main  MKSRoleMain: Powering on.
2024-01-20T08:10:07.196Z In(05) main  MKSSandbox: Powering on MKSSandboxComm
2024-01-20T08:10:07.196Z In(05) main  MKSRoleMain: Powering on MKS
2024-01-20T08:10:07.196Z In(05) main  MKS PowerOn
2024-01-20T08:10:07.197Z In(05) mks  VTHREAD 6292 "mks"
2024-01-20T08:10:07.197Z In(05) mks  MKS thread is alive
2024-01-20T08:10:07.197Z In(05) svga  VTHREAD 8108 "svga"
2024-01-20T08:10:07.197Z In(05) svga  SVGA thread is alive
2024-01-20T08:10:07.197Z In(05) mks  MKS: SSE2=1, SSSE3=1, SSE4_1=1
2024-01-20T08:10:07.209Z In(05) mks  MKS Win32: Registering top level window (0x40b00) to receive session change notification.
2024-01-20T08:10:07.211Z In(05) mks  MKS Win32: Successfully enabled high resolution system timer.
2024-01-20T08:10:07.211Z In(05) mks  Current Display Settings:
2024-01-20T08:10:07.212Z In(05) mks     Display: 0 size: 1280x1024  position: (0, 0) name: \\.\DISPLAY1  
2024-01-20T08:10:07.215Z In(05) mks  MKS Win32: MIL: 0x2000
2024-01-20T08:10:07.215Z In(05) mks  MKS-RenderMain: PowerOn allowed DX11Renderer 
2024-01-20T08:10:07.215Z In(05) mks  MKS-RenderMain: ISB not enabled by config
2024-01-20T08:10:07.215Z In(05) mks  MKS-RenderMain: Collecting RenderOps caps from DX11Renderer
2024-01-20T08:10:07.215Z In(05) mks  MKS-RenderMain: Starting DX11Renderer
2024-01-20T08:10:07.225Z In(05) mks  DX11Renderer: Enumerating adapter 0
2024-01-20T08:10:07.225Z In(05) mks  DX11Renderer: `AMD Radeon HD 5700 Series` vendor=0x1002 device=0x68be revision=0
2024-01-20T08:10:07.225Z In(05) mks  DX11Renderer: video=1010MB system=0MB shared=3840MB
2024-01-20T08:10:07.271Z In(05) mks  DX11Renderer: Using device unknown; adapter `AMD Radeon HD 5700 Series`
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: Enumerating adapter 1
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: `AMD Radeon HD 5700 Series` vendor=0x1002 device=0x68be revision=0
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: video=1010MB system=0MB shared=3840MB
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: Enumerating adapter 2
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: `Microsoft Basic Render Driver` vendor=0x1414 device=0x008c revision=0
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: video=0MB system=0MB shared=9215MB
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: LOCAL     budget  8498M usage     0M avail  4377M res     0M
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: NON-LOCAL budget     0M usage     0M avail     0M res     0M
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: GPU chip: Unknown (RDNA?)
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: Max 2D Texture Size=16384
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer:  ClearViewSupported: 1
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer:  MultisampleRTVWithForcedSampleCountOne: 1
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer:  MapNoOverwriteOnDynamicConstantBuffer: 1
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer:  MapNoOverwriteOnDynamicBufferSRV: 1
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer:  UseNativeConstantBuffers: 1
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer:  UseBlendLogicOps: 1
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer:  TypedUAVLoadAdditionalFormats: 0
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: SDK=7, feature level=11.0
2024-01-20T08:10:07.272Z In(05) mks  DX11Renderer: Max feature level=11.0
2024-01-20T08:10:07.332Z Wa(03) mks  DX11Renderer: AMDBGRAClearBug: no
2024-01-20T08:10:07.334Z In(05) mks  Started Shim3D
2024-01-20T08:10:07.334Z In(05) mks  MKS-RenderMain: Started DX11Renderer
2024-01-20T08:10:07.334Z In(05) mks  MKS-RenderMain: Found Full Renderer: DX11Renderer
2024-01-20T08:10:07.334Z In(05) mks  MKS-RenderMain: maxTextureSize=16384
2024-01-20T08:10:07.334Z In(05) mks  MKSRemoteMgr: Set default display name: Windows 10 lonele
2024-01-20T08:10:07.334Z In(05) mks  MKSRemoteMgr: Loading VNC Configuration from VM config file
2024-01-20T08:10:07.334Z In(05) mks  MKSRemoteMgr: Using default VNC keymap table "us"
2024-01-20T08:10:07.334Z In(05) main  hostCPUID vendor: AuthenticAMD
2024-01-20T08:10:07.334Z In(05) main  hostCPUID family: 0x10 model: 0x4 stepping: 0x3
2024-01-20T08:10:07.334Z In(05) main  hostCPUID codename: Shanghai (K10)
2024-01-20T08:10:07.334Z In(05) main  hostCPUID name: AMD Phenom(tm) II X4 945 Processor
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 00000000, 0: 0x00000005 0x68747541 0x444d4163 0x69746e65
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 00000001, 0: 0x00100f43 0x03040800 0x00802009 0x178bfbff
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 00000002, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 00000004, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000000, 0: 0x8000001b 0x68747541 0x444d4163 0x69746e65
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000001, 0: 0x00100f43 0x10001ad6 0x000037ff 0xefd3fbff
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000002, 0: 0x20444d41 0x6e656850 0x74286d6f 0x4920296d
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000003, 0: 0x34582049 0x35343920 0x6f725020 0x73736563
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000004, 0: 0x0000726f 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000005, 0: 0xff30ff10 0xff30ff20 0x40020140 0x40020140
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000006, 0: 0x20800000 0x42004200 0x02008140 0x0030b140
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x000001f9
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000008, 0: 0x00003030 0x00000000 0x00002003 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 8000000a, 0: 0x00000001 0x00000040 0x00000000 0x0000000f
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 8000000b, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 8000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 8000000d, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 8000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 8000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000012, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000014, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000015, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000016, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.334Z In(05) main  hostCPUID level 80000017, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.335Z In(05) main  hostCPUID level 80000018, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.335Z In(05) main  hostCPUID level 80000019, 0: 0xf0300000 0x60100000 0x00000000 0x00000000
2024-01-20T08:10:07.335Z In(05) main  hostCPUID level 8000001a, 0: 0x00000003 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.335Z In(05) main  hostCPUID level 8000001b, 0: 0x0000001f 0x00000000 0x00000000 0x00000000
2024-01-20T08:10:07.335Z In(05) main  PStrIntern expansion: nBkts=256
2024-01-20T08:10:07.335Z In(05) main  VMFeature_GetHostCaps: hostCaps:
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.sse3 = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.mwait = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.cmpxchg16b = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.popcnt = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.lahf64 = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.svm = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.extapicspc = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.cr8avail = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.abm = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.sse4a = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.misaligned_sse = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.3dnprefetch = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.nx = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.mmxext = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.ffxsr = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.pdpe1gb = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.rdtscp = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.lm = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.3dnowplus = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.3dnow = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.svm_npt = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.svm_nrip = 1
2024-01-20T08:10:07.335Z In(05) main  Capability Found: cpuid.amd = 1
2024-01-20T08:10:07.335Z In(05) main  MKSRoleMain: PowerOn finished.
2024-01-20T08:10:07.336Z In(05) svga  SVGA3dDevCaps: replayFifo host
2024-01-20T08:10:07.336Z In(05) svga    cap[  0]: 0x00000001 (3D)
2024-01-20T08:10:07.336Z In(05) svga    cap[  1]: 0x00000008 (MAX_LIGHTS)
2024-01-20T08:10:07.336Z In(05) svga    cap[  2]: 0x00000008 (MAX_TEXTURES)
2024-01-20T08:10:07.336Z In(05) svga    cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2024-01-20T08:10:07.336Z In(05) svga    cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2024-01-20T08:10:07.336Z In(05) svga    cap[  5]: 0x00000001 (VERTEX_SHADER)
2024-01-20T08:10:07.336Z In(05) svga    cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2024-01-20T08:10:07.336Z In(05) svga    cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2024-01-20T08:10:07.336Z In(05) svga    cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2024-01-20T08:10:07.336Z In(05) svga    cap[  9]: 0x00000001 (S23E8_TEXTURES)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 15]: 0x00000001 (QUERY_TYPES)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 17]:   1.000000 (MAX_POINT_SIZE)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2024-01-20T08:10:07.336Z In(05) svga    cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 62]: 0x00000000 (MISSING62)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 70]: 0x00000000 (DEAD4)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 71]: 0x00000000 (DEAD5)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 72]: 0x00000000 (DEAD7)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 73]: 0x00000000 (DEAD6)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 76]: 0x00000000 (DEAD10)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 84]: 0x00000000 (DEAD1)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 85]: 0x00000000 (DEAD8)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 86]: 0x00000000 (DEAD9)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 87]: 0x00000001 (LINE_AA)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 88]: 0x00000000 (LINE_STIPPLE)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 92]: 0x00000000 (DEAD3)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 94]: 0x00000000 (DEAD2)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 95]: 0x00000001 (DXCONTEXT)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 96]: 0x00000000 (DEAD11)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 98]: 0x0000000e (DX_MAX_CONSTANT_BUFFERS)
2024-01-20T08:10:07.337Z In(05) svga    cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2024-01-20T08:10:07.337Z In(05) svga    cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2024-01-20T08:10:07.337Z In(05) svga    cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2024-01-20T08:10:07.337Z In(05) svga    cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2024-01-20T08:10:07.337Z In(05) svga    cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2024-01-20T08:10:07.337Z In(05) svga    cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2024-01-20T08:10:07.337Z In(05) svga    cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2024-01-20T08:10:07.337Z In(05) svga    cap[106]: 0x00000009 (DXFMT_Z_D32)
2024-01-20T08:10:07.337Z In(05) svga    cap[107]: 0x0000026b (DXFMT_Z_D16)
2024-01-20T08:10:07.337Z In(05) svga    cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2024-01-20T08:10:07.337Z In(05) svga    cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2024-01-20T08:10:07.337Z In(05) svga    cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2024-01-20T08:10:07.337Z In(05) svga    cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2024-01-20T08:10:07.337Z In(05) svga    cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2024-01-20T08:10:07.337Z In(05) svga    cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2024-01-20T08:10:07.337Z In(05) svga    cap[114]: 0x00000063 (DXFMT_DXT1)
2024-01-20T08:10:07.337Z In(05) svga    cap[115]: 0x00000063 (DXFMT_DXT2)
2024-01-20T08:10:07.337Z In(05) svga    cap[116]: 0x00000063 (DXFMT_DXT3)
2024-01-20T08:10:07.337Z In(05) svga    cap[117]: 0x00000063 (DXFMT_DXT4)
2024-01-20T08:10:07.337Z In(05) svga    cap[118]: 0x00000063 (DXFMT_DXT5)
2024-01-20T08:10:07.337Z In(05) svga    cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2024-01-20T08:10:07.337Z In(05) svga    cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2024-01-20T08:10:07.337Z In(05) svga    cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2024-01-20T08:10:07.337Z In(05) svga    cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2024-01-20T08:10:07.337Z In(05) svga    cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2024-01-20T08:10:07.337Z In(05) svga    cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2024-01-20T08:10:07.337Z In(05) svga    cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2024-01-20T08:10:07.337Z In(05) svga    cap[126]: 0x000000e3 (DXFMT_V8U8)
2024-01-20T08:10:07.337Z In(05) svga    cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2024-01-20T08:10:07.337Z In(05) svga    cap[128]: 0x00000063 (DXFMT_CxV8U8)
2024-01-20T08:10:07.337Z In(05) svga    cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2024-01-20T08:10:07.337Z In(05) svga    cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2024-01-20T08:10:07.337Z In(05) svga    cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2024-01-20T08:10:07.337Z In(05) svga    cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2024-01-20T08:10:07.337Z In(05) svga    cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2024-01-20T08:10:07.337Z In(05) svga    cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2024-01-20T08:10:07.337Z In(05) svga    cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2024-01-20T08:10:07.337Z In(05) svga    cap[136]: 0x00000001 (DXFMT_BUFFER)
2024-01-20T08:10:07.337Z In(05) svga    cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2024-01-20T08:10:07.337Z In(05) svga    cap[138]: 0x000001e3 (DXFMT_V16U16)
2024-01-20T08:10:07.337Z In(05) svga    cap[139]: 0x000003f7 (DXFMT_G16R16)
2024-01-20T08:10:07.337Z In(05) svga    cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2024-01-20T08:10:07.338Z In(05) svga    cap[141]: 0x00000001 (DXFMT_UYVY)
2024-01-20T08:10:07.338Z In(05) svga    cap[142]: 0x00000041 (DXFMT_YUY2)
2024-01-20T08:10:07.338Z In(05) svga    cap[143]: 0x00000041 (DXFMT_NV12)
2024-01-20T08:10:07.338Z In(05) svga    cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2024-01-20T08:10:07.338Z In(05) svga    cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2024-01-20T08:10:07.338Z In(05) svga    cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2024-01-20T08:10:07.338Z In(05) svga    cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2024-01-20T08:10:07.338Z In(05) svga    cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2024-01-20T08:10:07.338Z In(05) svga    cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2024-01-20T08:10:07.338Z In(05) svga    cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2024-01-20T08:10:07.338Z In(05) svga    cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2024-01-20T08:10:07.338Z In(05) svga    cap[196]: 0x00000001 (DXFMT_P8)
2024-01-20T08:10:07.338Z In(05) svga    cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2024-01-20T08:10:07.338Z In(05) svga    cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2024-01-20T08:10:07.338Z In(05) svga    cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2024-01-20T08:10:07.338Z In(05) svga    cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2024-01-20T08:10:07.338Z In(05) svga    cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[207]: 0x00000063 (DXFMT_ATI1)
2024-01-20T08:10:07.338Z In(05) svga    cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[210]: 0x00000063 (DXFMT_ATI2)
2024-01-20T08:10:07.338Z In(05) svga    cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2024-01-20T08:10:07.338Z In(05) svga    cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2024-01-20T08:10:07.338Z In(05) svga    cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2024-01-20T08:10:07.338Z In(05) svga    cap[217]: 0x0000006b (DXFMT_Z_DF16)
2024-01-20T08:10:07.338Z In(05) svga    cap[218]: 0x0000006b (DXFMT_Z_DF24)
2024-01-20T08:10:07.338Z In(05) svga    cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2024-01-20T08:10:07.338Z In(05) svga    cap[220]: 0x00000001 (DXFMT_YV12)
2024-01-20T08:10:07.338Z In(05) svga    cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2024-01-20T08:10:07.338Z In(05) svga    cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2024-01-20T08:10:07.338Z In(05) svga    cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2024-01-20T08:10:07.338Z In(05) svga    cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2024-01-20T08:10:07.338Z In(05) svga    cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2024-01-20T08:10:07.338Z In(05) svga    cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2024-01-20T08:10:07.338Z In(05) svga    cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2024-01-20T08:10:07.338Z In(05) svga    cap[244]: 0x00000001 (SM41)
2024-01-20T08:10:07.338Z In(05) svga    cap[245]: 0x00000001 (MULTISAMPLE_2X)
2024-01-20T08:10:07.338Z In(05) svga    cap[246]: 0x00000001 (MULTISAMPLE_4X)
2024-01-20T08:10:07.338Z In(05) svga    cap[247]: 0x00000001 (MS_FULL_QUALITY)
2024-01-20T08:10:07.339Z In(05) svga    cap[248]: 0x00000001 (LOGICOPS)
2024-01-20T08:10:07.339Z In(05) svga    cap[249]: 0x00000001 (LOGIC_BLENDOPS)
2024-01-20T08:10:07.339Z In(05) svga    cap[250]: 0x00000000 (DEAD12)
2024-01-20T08:10:07.339Z In(05) svga    cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2024-01-20T08:10:07.339Z In(05) svga    cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2024-01-20T08:10:07.339Z In(05) svga    cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2024-01-20T08:10:07.339Z In(05) svga    cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2024-01-20T08:10:07.339Z In(05) svga    cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2024-01-20T08:10:07.339Z In(05) svga    cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2024-01-20T08:10:07.339Z In(05) svga    cap[257]: 0x00000000 (DEAD13)
2024-01-20T08:10:07.339Z In(05) svga    cap[258]: 0x00000001 (SM5)
2024-01-20T08:10:07.339Z In(05) svga    cap[259]: 0x00000001 (MULTISAMPLE_8X)
2024-01-20T08:10:07.339Z In(05) svga    cap[260]: 0x00000000 (MAX_FORCED_SAMPLE_COUNT)
2024-01-20T08:10:07.339Z In(05) svga    cap[261]: 0x00000000 (GL43)
2024-01-20T08:10:07.343Z In(05) svga  ReplayFifo: mksReplay format version=18
2024-01-20T08:10:07.343Z In(05) svga  MKSRoleReplay: MKSRoleReplay_Run: Starting.
2024-01-20T08:49:09.397Z In(05) mks  MKSThread: Requesting MKS exit
2024-01-20T08:49:09.397Z In(05) svga  MKSSandboxComm: ISB closed the channel while reading
2024-01-20T08:49:09.397Z In(05) svga  MKSRoleReplay: MKSRoleReplay_Run: Finished.
2024-01-20T08:49:09.400Z In(05) main  Stopping MKS/SVGA threads
2024-01-20T08:49:09.401Z In(05) main  MKS/SVGA threads are stopped
2024-01-20T08:49:09.401Z In(05) main  MKSRoleMain: Powering off.
2024-01-20T08:49:09.401Z In(05) main  MKSRoleMain: Powering off MKS
2024-01-20T08:49:09.405Z In(05) mks  Stopped Shim3D
2024-01-20T08:49:09.406Z In(05) mks  DX11Renderer: Clearing device context
2024-01-20T08:49:09.408Z In(05) mks  DX11Renderer: Release D3D device
2024-01-20T08:49:09.408Z In(05) mks  DX11Renderer: Releasing DXGI Adapter
2024-01-20T08:49:09.408Z In(05) mks  DX11Renderer: Releasing DXGI Factory
2024-01-20T08:49:09.408Z In(05) mks  DX11Renderer: Unloading modules
2024-01-20T08:49:09.408Z In(05) mks  MKS-RenderMain: Stopped DX11Renderer
2024-01-20T08:49:09.409Z In(05) mks  MKS PowerOff
2024-01-20T08:49:09.409Z In(05) svga  SVGA thread is exiting
2024-01-20T08:49:09.409Z In(05) mks  MKS thread is exiting
2024-01-20T08:49:09.410Z In(05) main  MKSRoleMain: Exiting...
2024-01-20T08:49:09.410Z In(05) main  MKSRoleMain: Exiting MKS
2024-01-20T08:49:09.410Z In(05) main  MKSRoleMain: Exiting Sig
2024-01-20T08:49:09.410Z In(05) main  MKSRoleMain: Skipping SSL (already exited)
2024-01-20T08:49:09.410Z In(05) main  MKSRoleMain: Exiting Poll
2024-01-20T08:49:09.410Z In(05) main  MKSRoleMain: Exiting Log
